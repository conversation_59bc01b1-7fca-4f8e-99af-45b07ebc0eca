"""
BOSS直聘安全检查绕过引擎
专门针对BOSS直聘的安全验证机制
"""

import asyncio
import random
import time
import re
import json
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, urlencode, urljoin

try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_PAGE_AVAILABLE = True
except ImportError:
    DRISSION_PAGE_AVAILABLE = False

try:
    import curl_cffi
    from curl_cffi import requests as cf_requests
    CURL_CFFI_AVAILABLE = True
except ImportError:
    CURL_CFFI_AVAILABLE = False

class BOSSSecurityBypass:
    """BOSS直聘安全检查绕过器"""
    
    def __init__(self):
        self.session = None
        self.cookies = {}
        self.user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        
    async def method_wait_and_extract_token(self, url: str) -> str:
        """方法1: 等待安全检查完成并提取token"""
        if not DRISSION_PAGE_AVAILABLE:
            return ""
        
        try:
            print("🚀 使用等待token方法...")
            
            # 配置Chrome选项
            options = ChromiumOptions()
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_user_agent(self.user_agent)
            
            # 启动浏览器
            page = ChromiumPage(addr_or_opts=options)
            
            try:
                print("访问目标页面...")
                page.get(url)
                
                # 等待安全检查页面加载
                await asyncio.sleep(3)
                
                # 检查是否是安全检查页面
                if "请稍候" in page.title or "正在加载中" in page.html:
                    print("检测到安全检查页面，智能等待处理...")

                    # 等待最多45秒让安全检查完成（增加等待时间）
                    for i in range(45):
                        await asyncio.sleep(1)
                        
                        # 检查页面是否已经跳转
                        current_url = page.url
                        current_title = page.title
                        
                        print(f"等待中... {i+1}/45 - 当前标题: {current_title}")

                        # 如果标题变了且不再是"请稍候"，说明跳转成功
                        if current_title != "请稍候" and "正在加载中" not in page.html:
                            print(f"✅ 安全检查完成！新标题: {current_title}")
                            break

                        # 检查是否有职位相关内容出现
                        if any(keyword in page.html for keyword in ["职位", "薪资", "工作", "公司"]):
                            print(f"✅ 检测到职位内容，安全检查可能已完成！")
                            break
                        
                        # 检查是否有__zp_stoken__ cookie
                        cookies = page.get_cookies()
                        for cookie in cookies:
                            if cookie.get('name') == '__zp_stoken__':
                                print(f"✅ 获取到安全token: {cookie.get('value')[:20]}...")
                                break
                
                # 获取最终页面内容
                final_html = page.html
                print(f"✅ 获取最终页面，长度: {len(final_html)}")
                
                return final_html
                
            finally:
                page.quit()
                
        except Exception as e:
            print(f"❌ 等待token方法失败: {str(e)}")
            return ""
    
    async def method_simulate_security_flow(self, url: str) -> str:
        """方法2: 模拟安全检查流程"""
        if not CURL_CFFI_AVAILABLE:
            return ""
        
        try:
            print("🚀 使用模拟安全流程方法...")
            
            session = cf_requests.Session()
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'max-age=0',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
            }
            
            # 第一步：访问页面获取安全检查
            print("步骤1: 获取安全检查页面...")
            response1 = session.get(url, headers=headers, impersonate="chrome120", timeout=30)
            
            if response1.status_code == 200 and "请稍候" in response1.text:
                print("检测到安全检查页面")
                
                # 解析安全检查参数
                security_params = self._parse_security_params(response1.text, response1.url)
                
                if security_params:
                    print(f"解析到安全参数: {security_params}")
                    
                    # 第二步：等待一段时间模拟加载
                    await asyncio.sleep(random.uniform(3, 6))
                    
                    # 第三步：尝试直接访问回调URL
                    if 'callbackUrl' in security_params:
                        callback_url = security_params['callbackUrl']
                        print(f"步骤2: 访问回调URL: {callback_url}")
                        
                        # 设置可能的token cookie
                        if 'seed' in security_params and 'ts' in security_params:
                            # 这里可以尝试生成token，但需要逆向JS
                            pass
                        
                        response2 = session.get(callback_url, headers=headers, impersonate="chrome120", timeout=30)
                        
                        if response2.status_code == 200 and len(response2.text) > 10000:
                            print(f"✅ 回调URL访问成功！页面长度: {len(response2.text)}")
                            return response2.text
            
            # 如果不是安全检查页面，直接返回
            if len(response1.text) > 10000:
                print(f"✅ 直接访问成功！页面长度: {len(response1.text)}")
                return response1.text
            
            return ""
            
        except Exception as e:
            print(f"❌ 模拟安全流程失败: {str(e)}")
            return ""
    
    def _parse_security_params(self, html: str, url: str) -> Dict[str, str]:
        """解析安全检查参数"""
        try:
            params = {}
            
            # 从URL中解析参数
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            for key in ['seed', 'ts', 'name', 'callbackUrl', 'srcReferer']:
                if key in query_params:
                    params[key] = query_params[key][0]
            
            # 从HTML中查找参数
            patterns = {
                'seed': r'l\("seed"\)\|\|"([^"]*)"',
                'ts': r'l\("ts"\)\|\|"([^"]*)"',
                'name': r'l\("name"\)\|\|"([^"]*)"',
                'callbackUrl': r'l\("callbackUrl"\)\|\|"([^"]*)"',
                'srcReferer': r'l\("srcReferer"\)\|\|"([^"]*)"'
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, html)
                if match:
                    params[key] = match.group(1)
            
            return params
            
        except Exception as e:
            print(f"解析安全参数失败: {str(e)}")
            return {}
    
    async def method_direct_bypass(self, url: str) -> str:
        """方法3: 直接绕过安全检查"""
        if not DRISSION_PAGE_AVAILABLE:
            return ""
        
        try:
            print("🚀 使用直接绕过方法...")
            
            # 配置Chrome选项 - 更激进的反检测
            options = ChromiumOptions()
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_argument('--disable-features=VizDisplayCompositor')
            options.set_argument('--disable-client-side-phishing-detection')
            options.set_argument('--disable-component-extensions-with-background-pages')
            options.set_argument('--disable-default-apps')
            options.set_argument('--disable-hang-monitor')
            options.set_argument('--disable-prompt-on-repost')
            options.set_argument('--disable-sync')
            options.set_argument('--metrics-recording-only')
            options.set_argument('--safebrowsing-disable-auto-update')
            options.set_argument('--password-store=basic')
            options.set_argument('--use-mock-keychain')
            options.set_user_agent(self.user_agent)
            
            # 启动浏览器
            page = ChromiumPage(addr_or_opts=options)
            
            try:
                # 注入强力反检测脚本
                bypass_script = """
                // 完全隐藏自动化痕迹
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                
                // 伪造Chrome环境
                window.chrome = {
                    runtime: {
                        onConnect: null,
                        onMessage: null
                    },
                    loadTimes: function() {
                        return {
                            commitLoadTime: Date.now() / 1000 - Math.random(),
                            finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                            finishLoadTime: Date.now() / 1000 - Math.random(),
                            firstPaintAfterLoadTime: 0,
                            firstPaintTime: Date.now() / 1000 - Math.random(),
                            navigationType: "Other",
                            npnNegotiatedProtocol: "h2",
                            requestTime: Date.now() / 1000 - Math.random(),
                            startLoadTime: Date.now() / 1000 - Math.random(),
                            wasAlternateProtocolAvailable: false,
                            wasFetchedViaSpdy: true,
                            wasNpnNegotiated: true
                        };
                    },
                    csi: function() {
                        return {
                            pageT: Date.now(),
                            startE: Date.now(),
                            tran: 15
                        };
                    }
                };
                
                // 删除所有自动化痕迹
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Reflect;
                
                // 伪造插件和语言
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en-US', 'en']});
                """
                
                page.run_js(bypass_script)
                
                # 先访问主页建立会话
                print("步骤1: 访问主页...")
                page.get("https://www.zhipin.com")
                await asyncio.sleep(random.uniform(2, 4))
                
                # 模拟搜索行为
                print("步骤2: 模拟搜索...")
                page.get("https://www.zhipin.com/web/geek/job")
                await asyncio.sleep(random.uniform(3, 5))
                
                # 访问目标页面
                print("步骤3: 访问目标页面...")
                page.get(url)
                
                # 如果遇到安全检查，等待处理
                if "请稍候" in page.title:
                    print("遇到安全检查，智能等待...")
                    
                    # 智能等待策略
                    max_wait = 45
                    for i in range(max_wait):
                        await asyncio.sleep(1)
                        
                        # 检查页面变化
                        current_title = page.title
                        current_html = page.html
                        
                        if i % 5 == 0:  # 每5秒打印一次状态
                            print(f"等待中... {i+1}/{max_wait} - 标题: {current_title}")
                        
                        # 检查是否完成
                        if current_title != "请稍候" and "正在加载中" not in current_html:
                            print(f"✅ 安全检查完成！")
                            break
                        
                        # 检查是否有职位内容
                        if any(keyword in current_html for keyword in ["职位", "薪资", "工作", "公司"]):
                            print(f"✅ 检测到职位内容！")
                            break
                
                # 获取最终页面
                final_html = page.html
                print(f"✅ 获取最终页面，长度: {len(final_html)}")
                
                return final_html
                
            finally:
                page.quit()
                
        except Exception as e:
            print(f"❌ 直接绕过方法失败: {str(e)}")
            return ""

# 全局实例
boss_security_bypass = BOSSSecurityBypass()
