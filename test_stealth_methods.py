"""
测试隐蔽反反爬方法
专门测试新的高级反检测技术
"""

import asyncio
import time
from boss_crawler import BOSSCrawler

async def test_stealth_methods():
    """测试隐蔽方法"""
    print("🚀 测试高级反反爬方法")
    print("=" * 60)
    
    # 使用一个真实的BOSS直聘URL
    test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
    
    start_time = time.time()
    
    async with BOSSCrawler() as crawler:
        print(f"🎯 测试URL: {test_url}")
        print(f"🔧 可用方法数量: {len(crawler.access_methods)}")
        
        # 只测试前3个方法（新的高级方法）
        for i, method in enumerate(crawler.access_methods[:3], 1):
            print(f"\n{'='*50}")
            print(f"🧪 测试方法 {i}: {method.__name__}")
            print(f"{'='*50}")
            
            try:
                html_content = await method(test_url)
                
                if html_content and len(html_content) > 1000:
                    print(f"✅ 方法 {i} 成功！页面长度: {len(html_content)}")
                    
                    # 检查是否是安全检查页面
                    if "安全验证" in html_content or "security" in html_content.lower() or "验证码" in html_content:
                        print(f"⚠️  仍然是安全检查页面")
                    else:
                        print(f"🎉 成功绕过安全检查！")
                        
                        # 尝试提取一些基本信息
                        if "职位" in html_content or "薪资" in html_content:
                            print(f"📋 页面包含职位信息")
                        
                        # 保存成功的页面内容用于分析
                        with open(f"success_page_{i}.html", "w", encoding="utf-8") as f:
                            f.write(html_content)
                        print(f"💾 页面内容已保存到 success_page_{i}.html")
                        
                        break  # 找到成功的方法就停止
                else:
                    print(f"❌ 方法 {i} 失败或内容太短")
                    
            except Exception as e:
                print(f"❌ 方法 {i} 异常: {str(e)}")
            
            print(f"⏱️  当前耗时: {time.time() - start_time:.2f} 秒")
    
    total_time = time.time() - start_time
    print(f"\n🏁 测试完成！总耗时: {total_time:.2f} 秒")

if __name__ == "__main__":
    asyncio.run(test_stealth_methods())
