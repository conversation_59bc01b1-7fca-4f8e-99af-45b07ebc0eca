"""
测试终极隐蔽引擎
验证GitHub上最强反反爬技术的效果
"""

import asyncio
import time
from boss_crawler import BOSSCrawler

async def test_ultimate_stealth():
    """测试终极隐蔽引擎"""
    print("🚀 测试终极隐蔽引擎 - GitHub最强反反爬技术")
    print("=" * 80)
    
    # 使用真实的BOSS直聘URL
    test_urls = [
        "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        "https://www.zhipin.com/job_detail/1234567890abcdef1XB93NS5FlRQ.html",  # 测试URL
    ]
    
    start_time = time.time()
    
    async with BOSSCrawler() as crawler:
        print(f"🎯 准备测试 {len(test_urls)} 个URL")
        print(f"🔧 可用方法数量: {len(crawler.access_methods)}")
        print(f"🚀 终极隐蔽方法: {crawler.access_methods[:3] if len(crawler.access_methods) >= 3 else crawler.access_methods}")
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n{'='*70}")
            print(f"🎯 测试 {i}/{len(test_urls)}: {url}")
            print(f"{'='*70}")
            
            # 只测试前3个终极隐蔽方法
            success = False
            for j, method in enumerate(crawler.access_methods[:3], 1):
                print(f"\n🧪 测试终极方法 {j}: {method.__name__}")
                print("-" * 50)
                
                try:
                    method_start = time.time()
                    html_content = await method(url)
                    method_time = time.time() - method_start
                    
                    if html_content and len(html_content) > 1000:
                        print(f"✅ 方法 {j} 成功！")
                        print(f"📊 页面长度: {len(html_content)}")
                        print(f"⏱️  方法耗时: {method_time:.2f} 秒")
                        
                        # 检查是否绕过了安全检查
                        security_keywords = [
                            "安全验证", "security", "验证码", "captcha", 
                            "cloudflare", "bot detection", "access denied",
                            "请稍后再试", "请求过于频繁", "blocked"
                        ]
                        
                        is_security_page = any(keyword in html_content.lower() for keyword in security_keywords)
                        
                        if is_security_page:
                            print(f"⚠️  仍然遇到安全检查页面")
                        else:
                            print(f"🎉 成功绕过安全检查！")
                            
                            # 检查是否包含职位信息
                            job_keywords = ["职位", "薪资", "工作", "公司", "岗位", "招聘"]
                            has_job_info = any(keyword in html_content for keyword in job_keywords)
                            
                            if has_job_info:
                                print(f"📋 页面包含职位信息")
                                
                                # 保存成功的页面
                                filename = f"ultimate_success_{i}_{j}.html"
                                with open(filename, "w", encoding="utf-8") as f:
                                    f.write(html_content)
                                print(f"💾 成功页面已保存: {filename}")
                                
                                success = True
                                break
                            else:
                                print(f"❓ 页面不包含职位信息")
                    else:
                        print(f"❌ 方法 {j} 失败或内容太短")
                        
                except Exception as e:
                    print(f"❌ 方法 {j} 异常: {str(e)}")
                
                print(f"⏱️  当前总耗时: {time.time() - start_time:.2f} 秒")
            
            if success:
                print(f"\n🎉 URL {i} 测试成功！")
                break
            else:
                print(f"\n😞 URL {i} 所有终极方法都失败了")
    
    total_time = time.time() - start_time
    print(f"\n🏁 终极隐蔽引擎测试完成！")
    print(f"⏱️  总耗时: {total_time:.2f} 秒")
    print(f"🚀 使用了GitHub上最强的反反爬技术组合")

if __name__ == "__main__":
    asyncio.run(test_ultimate_stealth())
