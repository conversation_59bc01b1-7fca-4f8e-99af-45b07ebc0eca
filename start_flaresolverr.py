"""
启动FlareSolverr服务
用于绕过Cloudflare保护
"""

import subprocess
import time
import requests
import sys
import os

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker已安装")
            return True
        else:
            print("❌ Docker未安装")
            return False
    except FileNotFoundError:
        print("❌ Docker未找到")
        return False

def check_flaresolverr_running():
    """检查FlareSolverr是否已经在运行"""
    try:
        response = requests.get("http://localhost:8191/v1", timeout=5)
        if response.status_code == 200:
            print("✅ FlareSolverr已在运行")
            return True
    except:
        pass
    
    print("❌ FlareSolverr未运行")
    return False

def start_flaresolverr():
    """启动FlareSolverr Docker容器"""
    if check_flaresolverr_running():
        return True
    
    if not check_docker():
        print("请先安装Docker: https://docs.docker.com/get-docker/")
        return False
    
    print("🚀 启动FlareSolverr Docker容器...")
    
    # 停止可能存在的旧容器
    try:
        subprocess.run(['docker', 'stop', 'flaresolverr'], capture_output=True)
        subprocess.run(['docker', 'rm', 'flaresolverr'], capture_output=True)
    except:
        pass
    
    # 启动新容器
    cmd = [
        'docker', 'run', '-d',
        '--name=flaresolverr',
        '-p', '8191:8191',
        '-e', 'LOG_LEVEL=info',
        '--restart', 'unless-stopped',
        'ghcr.io/flaresolverr/flaresolverr:latest'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FlareSolverr容器启动成功")
            
            # 等待服务启动
            print("⏳ 等待FlareSolverr服务启动...")
            for i in range(30):
                time.sleep(2)
                if check_flaresolverr_running():
                    print("🎉 FlareSolverr服务已就绪！")
                    return True
                print(f"等待中... ({i+1}/30)")
            
            print("❌ FlareSolverr服务启动超时")
            return False
        else:
            print(f"❌ FlareSolverr容器启动失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动FlareSolverr异常: {str(e)}")
        return False

def stop_flaresolverr():
    """停止FlareSolverr服务"""
    try:
        print("🛑 停止FlareSolverr服务...")
        subprocess.run(['docker', 'stop', 'flaresolverr'], capture_output=True)
        subprocess.run(['docker', 'rm', 'flaresolverr'], capture_output=True)
        print("✅ FlareSolverr服务已停止")
    except Exception as e:
        print(f"❌ 停止FlareSolverr异常: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "stop":
        stop_flaresolverr()
        return
    
    print("🚀 FlareSolverr启动器")
    print("=" * 50)
    
    if start_flaresolverr():
        print("\n🎉 FlareSolverr已成功启动！")
        print("📍 服务地址: http://localhost:8191")
        print("📖 现在可以运行爬虫测试了")
        print("\n💡 提示:")
        print("  - 运行 'python test_ultimate_stealth.py' 测试爬虫")
        print("  - 运行 'python start_flaresolverr.py stop' 停止服务")
    else:
        print("\n❌ FlareSolverr启动失败")
        print("💡 请检查:")
        print("  - Docker是否正确安装")
        print("  - 端口8191是否被占用")
        print("  - 网络连接是否正常")

if __name__ == "__main__":
    main()
