"""
BOSS直聘爬虫主程序
集成高级功能的职位信息爬取工具
"""

import asyncio
import sys
import os
from advanced_crawler import advanced_crawler
from config_manager import config_manager
from logger_manager import logger
from workflow_manager import workflow_manager
from smart_url_collector import smart_url_collector
from performance_optimizer import performance_optimizer
from system_health_checker import system_health_checker

def print_banner():
    """打印程序横幅"""
    print("🚀 BOSS直聘高级爬虫系统")
    print("=" * 60)
    print("✅ 成功绕过安全检查")
    print("✅ 智能重试机制")
    print("✅ 数据质量分析")
    print("✅ 多格式输出")
    print("=" * 60)

def print_usage():
    """打印使用说明"""
    print("📋 使用方法:")
    print("  python main.py                    # 交互式模式")
    print("  python main.py urls.txt           # 从文件批量爬取")
    print("  python main.py --config           # 查看配置")
    print("  python main.py --sample-config    # 创建示例配置")
    print("")

async def interactive_mode():
    """交互式模式"""
    print("🎯 交互式模式")
    print("-" * 30)

    while True:
        print("\n请选择操作:")
        print("1. 爬取单个职位URL")
        print("2. 批量爬取多个URL")
        print("3. 从文件批量爬取")
        print("4. 智能搜索收集URL")
        print("5. 完整工作流（搜索+爬取+分析）")
        print("6. 查看性能统计")
        print("7. 系统健康检查")
        print("8. 查看配置")
        print("9. 退出")

        choice = input("\n请输入选择 (1-9): ").strip()

        if choice == "1":
            await single_url_mode()
        elif choice == "2":
            await multiple_urls_mode()
        elif choice == "3":
            await file_mode()
        elif choice == "4":
            await smart_search_mode()
        elif choice == "5":
            await complete_workflow_mode()
        elif choice == "6":
            performance_optimizer.print_real_time_stats()
        elif choice == "7":
            await system_health_check_mode()
        elif choice == "8":
            config_manager.print_config()
        elif choice == "9":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

async def single_url_mode():
    """单个URL模式"""
    url = input("请输入职位URL: ").strip()

    if not url.startswith("https://www.zhipin.com/job_detail/"):
        print("❌ 无效的BOSS直聘职位URL")
        return

    print(f"\n🎯 开始爬取: {url}")

    try:
        from boss_crawler import BOSSCrawler
        async with BOSSCrawler() as crawler:
            job_data = await crawler.crawl_job_detail(url)

            if job_data:
                print("✅ 爬取成功！")
                print("-" * 30)
                for key, value in job_data.items():
                    if value and value.strip():
                        display_value = value[:80] + "..." if len(value) > 80 else value
                        print(f"{key}: {display_value}")
                    else:
                        print(f"{key}: (未提取到)")
            else:
                print("❌ 爬取失败")

    except Exception as e:
        print(f"❌ 爬取异常: {str(e)}")

async def multiple_urls_mode():
    """多个URL模式"""
    print("请输入多个URL（每行一个，输入空行结束）:")
    urls = []

    while True:
        url = input().strip()
        if not url:
            break
        if url.startswith("https://www.zhipin.com/job_detail/"):
            urls.append(url)
        else:
            print(f"⚠️  跳过无效URL: {url}")

    if not urls:
        print("❌ 没有有效的URL")
        return

    output_prefix = input(f"输出文件前缀 (默认: boss_jobs): ").strip() or "boss_jobs"

    print(f"\n🎯 开始批量爬取 {len(urls)} 个URL...")
    result = await advanced_crawler.crawl_batch_advanced(urls, output_prefix)

    if result.get("analysis_report"):
        print("\n📊 数据分析报告:")
        print(result["analysis_report"])

async def smart_search_mode():
    """智能搜索模式"""
    print("🔍 智能搜索收集URL")
    print("-" * 30)

    keyword = input("请输入搜索关键词 (可选): ").strip()
    city = input("请输入城市 (可选，如：北京、上海): ").strip()
    pages = input("请输入搜索页数 (默认: 3): ").strip()

    try:
        pages = int(pages) if pages else 3
    except ValueError:
        pages = 3

    print(f"\n🎯 开始搜索收集...")

    try:
        urls = await smart_url_collector.collect_job_urls(keyword, city, pages)

        if urls:
            print(f"✅ 收集成功！共找到 {len(urls)} 个职位URL")

            # 保存到文件
            filename = smart_url_collector.save_urls_to_file(urls, "collected_urls.txt")
            if filename:
                print(f"💾 URL已保存到: {filename}")

            # 询问是否立即爬取
            crawl_now = input("\n是否立即开始爬取这些URL？(y/n): ").strip().lower()
            if crawl_now == 'y':
                output_prefix = input("输出文件前缀 (默认: smart_crawl): ").strip() or "smart_crawl"
                result = await advanced_crawler.crawl_batch_advanced(urls, output_prefix)

                if result.get("analysis_report"):
                    print("\n📊 数据分析报告:")
                    print(result["analysis_report"])
        else:
            print("❌ 未找到任何职位URL")

    except Exception as e:
        print(f"❌ 搜索异常: {str(e)}")

async def complete_workflow_mode():
    """完整工作流模式"""
    print("🚀 完整工作流模式")
    print("-" * 30)
    print("这将执行：搜索收集 → 数据爬取 → 数据分析 → 性能优化")

    # 收集配置
    workflow_config = {}

    print("\n📋 配置工作流:")

    # 搜索配置
    keyword = input("搜索关键词 (可选): ").strip()
    city = input("城市 (可选): ").strip()
    pages = input("每个搜索条件的页数 (默认: 3): ").strip()

    try:
        pages = int(pages) if pages else 3
    except ValueError:
        pages = 3

    workflow_config.update({
        "collection_method": "search",
        "keyword": keyword,
        "city": city,
        "pages": pages
    })

    print(f"\n🚀 开始执行完整工作流...")

    try:
        result = await workflow_manager.run_complete_workflow(workflow_config)

        if result.get("error"):
            print(f"❌ 工作流失败: {result['error']}")
        else:
            print(f"\n🎉 完整工作流执行成功！")
            print(f"📊 工作流ID: {result['workflow_id']}")
            print(f"⏱️  总耗时: {result['total_duration']:.1f} 秒")

            summary = result.get("summary", {})
            print(f"\n📈 执行摘要:")
            print(f"  收集URL: {summary.get('total_urls_collected', 0)} 个")
            print(f"  成功爬取: {summary.get('total_jobs_crawled', 0)} 个")
            print(f"  总体成功率: {summary.get('overall_success_rate', 0):.1f}%")
            print(f"  数据质量分数: {summary.get('data_quality_score', 0):.1f}/100")
            print(f"  工作流效率: {summary.get('workflow_efficiency', 0):.1f}/100")

            files = result.get("files_generated", [])
            if files:
                print(f"\n📁 生成的文件:")
                for file in files:
                    print(f"  📄 {file}")

            recommendations = result.get("recommendations", [])
            if recommendations:
                print(f"\n💡 优化建议:")
                for rec in recommendations:
                    print(f"  • {rec}")

    except Exception as e:
        print(f"❌ 工作流异常: {str(e)}")

async def system_health_check_mode():
    """系统健康检查模式"""
    print("🏥 系统健康检查")
    print("-" * 30)

    try:
        health_report = await system_health_checker.run_full_health_check()
        system_health_checker.print_health_report(health_report)

        # 询问是否查看详细信息
        detail_choice = input("\n是否查看详细信息？(y/n): ").strip().lower()
        if detail_choice == 'y':
            print("\n📋 详细健康报告:")
            print("=" * 50)

            import json
            print(json.dumps(health_report, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")

async def file_mode():
    """文件模式"""
    file_path = input("请输入URL文件路径 (默认: urls.txt): ").strip() or "urls.txt"

    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return

    output_prefix = input(f"输出文件前缀 (默认: boss_jobs): ").strip() or "boss_jobs"

    print(f"\n🎯 从文件批量爬取: {file_path}")
    result = await advanced_crawler.crawl_from_file(file_path, output_prefix)

    if result.get("error"):
        print(f"❌ 错误: {result['error']}")
    elif result.get("analysis_report"):
        print("\n📊 数据分析报告:")
        print(result["analysis_report"])

async def main():
    """主函数"""
    print_banner()

    # 检查命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]

        if arg == "--config":
            config_manager.print_config()
            return
        elif arg == "--sample-config":
            config_manager.create_sample_config()
            return
        elif arg == "--help" or arg == "-h":
            print_usage()
            return
        elif os.path.exists(arg):
            # 文件模式
            print(f"🎯 从文件批量爬取: {arg}")
            result = await advanced_crawler.crawl_from_file(arg)

            if result.get("error"):
                print(f"❌ 错误: {result['error']}")
            elif result.get("analysis_report"):
                print("\n📊 数据分析报告:")
                print(result["analysis_report"])
            return
        else:
            print(f"❌ 文件不存在: {arg}")
            print_usage()
            return

    # 交互式模式
    await interactive_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
