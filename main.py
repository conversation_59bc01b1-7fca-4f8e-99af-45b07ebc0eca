"""
BOSS直聘爬虫主程序
集成高级功能的职位信息爬取工具
"""

import asyncio
import sys
import os
from advanced_crawler import advanced_crawler
from config_manager import config_manager
from logger_manager import logger

def print_banner():
    """打印程序横幅"""
    print("🚀 BOSS直聘高级爬虫系统")
    print("=" * 60)
    print("✅ 成功绕过安全检查")
    print("✅ 智能重试机制")
    print("✅ 数据质量分析")
    print("✅ 多格式输出")
    print("=" * 60)

def print_usage():
    """打印使用说明"""
    print("📋 使用方法:")
    print("  python main.py                    # 交互式模式")
    print("  python main.py urls.txt           # 从文件批量爬取")
    print("  python main.py --config           # 查看配置")
    print("  python main.py --sample-config    # 创建示例配置")
    print("")

async def interactive_mode():
    """交互式模式"""
    print("🎯 交互式模式")
    print("-" * 30)

    while True:
        print("\n请选择操作:")
        print("1. 爬取单个职位URL")
        print("2. 批量爬取多个URL")
        print("3. 从文件批量爬取")
        print("4. 查看配置")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            await single_url_mode()
        elif choice == "2":
            await multiple_urls_mode()
        elif choice == "3":
            await file_mode()
        elif choice == "4":
            config_manager.print_config()
        elif choice == "5":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

async def single_url_mode():
    """单个URL模式"""
    url = input("请输入职位URL: ").strip()

    if not url.startswith("https://www.zhipin.com/job_detail/"):
        print("❌ 无效的BOSS直聘职位URL")
        return

    print(f"\n🎯 开始爬取: {url}")

    try:
        from boss_crawler import BOSSCrawler
        async with BOSSCrawler() as crawler:
            job_data = await crawler.crawl_job_detail(url)

            if job_data:
                print("✅ 爬取成功！")
                print("-" * 30)
                for key, value in job_data.items():
                    if value and value.strip():
                        display_value = value[:80] + "..." if len(value) > 80 else value
                        print(f"{key}: {display_value}")
                    else:
                        print(f"{key}: (未提取到)")
            else:
                print("❌ 爬取失败")

    except Exception as e:
        print(f"❌ 爬取异常: {str(e)}")

async def multiple_urls_mode():
    """多个URL模式"""
    print("请输入多个URL（每行一个，输入空行结束）:")
    urls = []

    while True:
        url = input().strip()
        if not url:
            break
        if url.startswith("https://www.zhipin.com/job_detail/"):
            urls.append(url)
        else:
            print(f"⚠️  跳过无效URL: {url}")

    if not urls:
        print("❌ 没有有效的URL")
        return

    output_prefix = input(f"输出文件前缀 (默认: boss_jobs): ").strip() or "boss_jobs"

    print(f"\n🎯 开始批量爬取 {len(urls)} 个URL...")
    result = await advanced_crawler.crawl_batch_advanced(urls, output_prefix)

    if result.get("analysis_report"):
        print("\n📊 数据分析报告:")
        print(result["analysis_report"])

async def file_mode():
    """文件模式"""
    file_path = input("请输入URL文件路径 (默认: urls.txt): ").strip() or "urls.txt"

    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return

    output_prefix = input(f"输出文件前缀 (默认: boss_jobs): ").strip() or "boss_jobs"

    print(f"\n🎯 从文件批量爬取: {file_path}")
    result = await advanced_crawler.crawl_from_file(file_path, output_prefix)

    if result.get("error"):
        print(f"❌ 错误: {result['error']}")
    elif result.get("analysis_report"):
        print("\n📊 数据分析报告:")
        print(result["analysis_report"])

async def main():
    """主函数"""
    print_banner()

    # 检查命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]

        if arg == "--config":
            config_manager.print_config()
            return
        elif arg == "--sample-config":
            config_manager.create_sample_config()
            return
        elif arg == "--help" or arg == "-h":
            print_usage()
            return
        elif os.path.exists(arg):
            # 文件模式
            print(f"🎯 从文件批量爬取: {arg}")
            result = await advanced_crawler.crawl_from_file(arg)

            if result.get("error"):
                print(f"❌ 错误: {result['error']}")
            elif result.get("analysis_report"):
                print("\n📊 数据分析报告:")
                print(result["analysis_report"])
            return
        else:
            print(f"❌ 文件不存在: {arg}")
            print_usage()
            return

    # 交互式模式
    await interactive_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
