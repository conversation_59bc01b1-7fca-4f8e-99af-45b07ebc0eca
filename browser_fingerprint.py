"""
浏览器指纹伪造模块
基于GitHub最新技术，深度伪造浏览器指纹
"""

import random
import json
import base64
from typing import Dict, Any, List

class BrowserFingerprint:
    """浏览器指纹伪造类"""
    
    def __init__(self):
        self.fingerprints = self._generate_fingerprint_pool()
    
    def _generate_fingerprint_pool(self) -> List[Dict]:
        """生成指纹池"""
        return [
            {
                "screen": {"width": 1920, "height": 1080, "colorDepth": 24},
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "platform": "Win32",
                "cookieEnabled": True,
                "doNotTrack": None,
                "hardwareConcurrency": 8
            },
            {
                "screen": {"width": 1366, "height": 768, "colorDepth": 24},
                "timezone": "Asia/Shanghai", 
                "language": "zh-CN",
                "platform": "Win32",
                "cookieEnabled": True,
                "doNotTrack": None,
                "hardwareConcurrency": 4
            },
            {
                "screen": {"width": 1440, "height": 900, "colorDepth": 24},
                "timezone": "Asia/Shanghai",
                "language": "zh-CN", 
                "platform": "MacIntel",
                "cookieEnabled": True,
                "doNotTrack": None,
                "hardwareConcurrency": 8
            }
        ]
    
    def get_random_fingerprint(self) -> Dict:
        """获取随机指纹"""
        return random.choice(self.fingerprints)
    
    def get_webgl_fingerprint_script(self) -> str:
        """WebGL指纹伪造脚本"""
        vendors = [
            "Google Inc. (Intel)",
            "Google Inc. (NVIDIA)",
            "Google Inc. (AMD)",
            "Mozilla",
            "WebKit"
        ]
        
        renderers = [
            "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)"
        ]
        
        vendor = random.choice(vendors)
        renderer = random.choice(renderers)
        
        return f"""
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{
                return '{vendor}';
            }}
            if (parameter === 37446) {{
                return '{renderer}';
            }}
            return getParameter.apply(this, arguments);
        }};
        
        const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
        WebGL2RenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{
                return '{vendor}';
            }}
            if (parameter === 37446) {{
                return '{renderer}';
            }}
            return getParameter2.apply(this, arguments);
        }};
        """
    
    def get_canvas_fingerprint_script(self) -> str:
        """Canvas指纹伪造脚本"""
        noise_factor = random.uniform(0.0001, 0.001)
        
        return f"""
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        
        HTMLCanvasElement.prototype.toDataURL = function() {{
            const context = this.getContext('2d');
            const imageData = context.getImageData(0, 0, this.width, this.height);
            
            for (let i = 0; i < imageData.data.length; i += 4) {{
                imageData.data[i] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
                imageData.data[i + 1] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
                imageData.data[i + 2] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
            }}
            
            context.putImageData(imageData, 0, 0);
            return originalToDataURL.apply(this, arguments);
        }};
        
        CanvasRenderingContext2D.prototype.getImageData = function() {{
            const imageData = originalGetImageData.apply(this, arguments);
            
            for (let i = 0; i < imageData.data.length; i += 4) {{
                imageData.data[i] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
                imageData.data[i + 1] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
                imageData.data[i + 2] += Math.floor(Math.random() * {noise_factor * 1000}) - {noise_factor * 500};
            }}
            
            return imageData;
        }};
        """
    
    def get_audio_fingerprint_script(self) -> str:
        """音频指纹伪造脚本"""
        noise_factor = random.uniform(0.00001, 0.0001)
        
        return f"""
        const originalGetChannelData = AudioBuffer.prototype.getChannelData;
        AudioBuffer.prototype.getChannelData = function(channel) {{
            const originalChannelData = originalGetChannelData.apply(this, arguments);
            
            for (let i = 0; i < originalChannelData.length; i++) {{
                originalChannelData[i] = originalChannelData[i] + (Math.random() - 0.5) * {noise_factor};
            }}
            
            return originalChannelData;
        }};
        
        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
        AudioContext.prototype.createAnalyser = function() {{
            const analyser = originalCreateAnalyser.apply(this, arguments);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
            
            analyser.getFloatFrequencyData = function(array) {{
                originalGetFloatFrequencyData.apply(this, arguments);
                for (let i = 0; i < array.length; i++) {{
                    array[i] += (Math.random() - 0.5) * {noise_factor};
                }}
            }};
            
            return analyser;
        }};
        """
    
    def get_screen_fingerprint_script(self) -> str:
        """屏幕指纹伪造脚本"""
        fingerprint = self.get_random_fingerprint()
        screen = fingerprint["screen"]
        
        return f"""
        Object.defineProperty(screen, 'width', {{
            get: () => {screen["width"]}
        }});
        
        Object.defineProperty(screen, 'height', {{
            get: () => {screen["height"]}
        }});
        
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {screen["colorDepth"]}
        }});
        
        Object.defineProperty(screen, 'pixelDepth', {{
            get: () => {screen["colorDepth"]}
        }});
        
        Object.defineProperty(screen, 'availWidth', {{
            get: () => {screen["width"]}
        }});
        
        Object.defineProperty(screen, 'availHeight', {{
            get: () => {screen["height"] - 40}
        }});
        """
    
    def get_navigator_fingerprint_script(self) -> str:
        """Navigator指纹伪造脚本"""
        fingerprint = self.get_random_fingerprint()
        
        return f"""
        Object.defineProperty(navigator, 'platform', {{
            get: () => '{fingerprint["platform"]}'
        }});
        
        Object.defineProperty(navigator, 'language', {{
            get: () => '{fingerprint["language"]}'
        }});
        
        Object.defineProperty(navigator, 'languages', {{
            get: () => ['{fingerprint["language"]}', 'zh', 'en-US', 'en']
        }});
        
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint["hardwareConcurrency"]}
        }});
        
        Object.defineProperty(navigator, 'cookieEnabled', {{
            get: () => {str(fingerprint["cookieEnabled"]).lower()}
        }});
        
        Object.defineProperty(navigator, 'doNotTrack', {{
            get: () => {fingerprint["doNotTrack"] if fingerprint["doNotTrack"] else "null"}
        }});
        
        // 隐藏webdriver痕迹
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined
        }});
        
        // 伪造插件
        Object.defineProperty(navigator, 'plugins', {{
            get: () => {{
                const plugins = [];
                plugins.length = {random.randint(3, 8)};
                return plugins;
            }}
        }});
        """
    
    def get_timezone_script(self) -> str:
        """时区伪造脚本"""
        fingerprint = self.get_random_fingerprint()
        
        return f"""
        const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
        Date.prototype.getTimezoneOffset = function() {{
            return -480; // 北京时间 UTC+8
        }};
        
        if (Intl && Intl.DateTimeFormat) {{
            const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
            Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                const options = originalResolvedOptions.apply(this, arguments);
                options.timeZone = '{fingerprint["timezone"]}';
                return options;
            }};
        }}
        """
    
    def get_complete_fingerprint_script(self) -> str:
        """获取完整的指纹伪造脚本"""
        scripts = [
            self.get_webgl_fingerprint_script(),
            self.get_canvas_fingerprint_script(),
            self.get_audio_fingerprint_script(),
            self.get_screen_fingerprint_script(),
            self.get_navigator_fingerprint_script(),
            self.get_timezone_script()
        ]
        
        return "\n".join(scripts)

# 全局实例
browser_fingerprint = BrowserFingerprint()
