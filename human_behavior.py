"""
人类行为模拟模块
基于真实人类行为模式，深度模拟用户操作
"""

import asyncio
import random
import math
import time
from typing import Tuple, List, Dict, Any

class HumanBehavior:
    """人类行为模拟类"""
    
    def __init__(self):
        self.last_action_time = time.time()
        self.action_history = []
        self.reading_speed = random.uniform(200, 400)  # 每分钟字数
        
    async def human_delay(self, min_delay: float = 1.0, max_delay: float = 3.0) -> None:
        """人类化延时"""
        # 基于真实人类反应时间的延时模式
        base_delay = random.uniform(min_delay, max_delay)
        
        # 添加微观延时模拟神经反应
        micro_delays = []
        for _ in range(random.randint(2, 5)):
            micro_delays.append(random.uniform(0.05, 0.2))
        
        # 执行微观延时
        for micro_delay in micro_delays:
            await asyncio.sleep(micro_delay)
        
        # 执行主要延时
        remaining_delay = max(0, base_delay - sum(micro_delays))
        await asyncio.sleep(remaining_delay)
        
        self.last_action_time = time.time()
    
    def generate_mouse_path(self, start_x: int, start_y: int, end_x: int, end_y: int, 
                          steps: int = None) -> List[Tuple[int, int]]:
        """生成人类化鼠标移动路径"""
        if steps is None:
            distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
            steps = max(10, int(distance / 10))
        
        path = []
        
        # 使用贝塞尔曲线模拟自然鼠标移动
        control_x = start_x + random.randint(-50, 50)
        control_y = start_y + random.randint(-50, 50)
        
        for i in range(steps + 1):
            t = i / steps
            
            # 二次贝塞尔曲线
            x = (1-t)**2 * start_x + 2*(1-t)*t * control_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * control_y + t**2 * end_y
            
            # 添加随机抖动模拟手部微颤
            jitter_x = random.randint(-2, 2)
            jitter_y = random.randint(-2, 2)
            
            path.append((int(x + jitter_x), int(y + jitter_y)))
        
        return path
    
    async def simulate_mouse_movement(self, driver, target_x: int, target_y: int) -> None:
        """模拟人类鼠标移动"""
        try:
            # 获取当前鼠标位置（模拟）
            current_x = random.randint(100, 800)
            current_y = random.randint(100, 600)
            
            # 生成移动路径
            path = self.generate_mouse_path(current_x, current_y, target_x, target_y)
            
            # 执行移动
            for x, y in path:
                await driver.execute_script(f"""
                    const event = new MouseEvent('mousemove', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    document.dispatchEvent(event);
                """)
                await asyncio.sleep(random.uniform(0.01, 0.03))
                
        except Exception as e:
            print(f"鼠标移动模拟失败: {e}")
    
    async def simulate_reading_behavior(self, driver, content_length: int = 1000) -> None:
        """模拟阅读行为"""
        try:
            # 计算阅读时间（基于内容长度和阅读速度）
            reading_time = (content_length / self.reading_speed) * 60
            reading_time = max(2.0, min(reading_time, 30.0))  # 限制在2-30秒
            
            # 模拟阅读过程中的滚动
            scroll_count = random.randint(2, 6)
            scroll_interval = reading_time / scroll_count
            
            for i in range(scroll_count):
                # 随机滚动距离
                scroll_amount = random.randint(100, 400)
                direction = 1 if random.random() > 0.1 else -1  # 90%向下，10%向上
                
                await driver.execute_script(f"window.scrollBy(0, {scroll_amount * direction});")
                
                # 阅读停顿
                pause_time = scroll_interval + random.uniform(-1, 1)
                await asyncio.sleep(max(0.5, pause_time))
                
        except Exception as e:
            print(f"阅读行为模拟失败: {e}")
    
    async def simulate_page_interaction(self, driver) -> None:
        """模拟页面交互行为"""
        try:
            interactions = [
                self._simulate_hover,
                self._simulate_focus,
                self._simulate_scroll_pause,
                self._simulate_window_resize
            ]
            
            # 随机选择1-3个交互行为
            selected_interactions = random.sample(interactions, random.randint(1, 3))
            
            for interaction in selected_interactions:
                await interaction(driver)
                await self.human_delay(0.5, 2.0)
                
        except Exception as e:
            print(f"页面交互模拟失败: {e}")
    
    async def _simulate_hover(self, driver) -> None:
        """模拟悬停行为"""
        await driver.execute_script("""
            const elements = document.querySelectorAll('a, button, [onclick]');
            if (elements.length > 0) {
                const randomElement = elements[Math.floor(Math.random() * elements.length)];
                const event = new MouseEvent('mouseenter', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                randomElement.dispatchEvent(event);
                
                setTimeout(() => {
                    const leaveEvent = new MouseEvent('mouseleave', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    randomElement.dispatchEvent(leaveEvent);
                }, Math.random() * 2000 + 500);
            }
        """)
    
    async def _simulate_focus(self, driver) -> None:
        """模拟焦点变化"""
        await driver.execute_script("""
            const focusableElements = document.querySelectorAll('input, textarea, select, button, a');
            if (focusableElements.length > 0) {
                const randomElement = focusableElements[Math.floor(Math.random() * focusableElements.length)];
                randomElement.focus();
                setTimeout(() => randomElement.blur(), Math.random() * 1000 + 200);
            }
        """)
    
    async def _simulate_scroll_pause(self, driver) -> None:
        """模拟滚动停顿"""
        scroll_amount = random.randint(50, 200)
        await driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
        await asyncio.sleep(random.uniform(1, 3))
        
        # 有时候会回滚一点
        if random.random() < 0.3:
            back_scroll = random.randint(10, 50)
            await driver.execute_script(f"window.scrollBy(0, -{back_scroll});")
    
    async def _simulate_window_resize(self, driver) -> None:
        """模拟窗口大小调整"""
        try:
            current_size = await driver.execute_script("return [window.innerWidth, window.innerHeight];")
            if current_size:
                width, height = current_size
                # 微小的窗口大小变化
                new_width = width + random.randint(-20, 20)
                new_height = height + random.randint(-20, 20)
                await driver.set_window_size(max(800, new_width), max(600, new_height))
        except:
            pass
    
    def get_human_typing_delays(self, text: str) -> List[float]:
        """生成人类化打字延时"""
        delays = []
        
        for i, char in enumerate(text):
            base_delay = random.uniform(0.05, 0.15)
            
            # 特殊字符需要更长时间
            if char in '!@#$%^&*()_+-=[]{}|;:,.<>?':
                base_delay *= random.uniform(1.5, 2.5)
            
            # 大写字母需要更长时间（Shift键）
            elif char.isupper():
                base_delay *= random.uniform(1.2, 1.8)
            
            # 数字键需要稍长时间
            elif char.isdigit():
                base_delay *= random.uniform(1.1, 1.4)
            
            # 偶尔的长停顿（思考时间）
            if random.random() < 0.05:
                base_delay += random.uniform(0.5, 2.0)
            
            delays.append(base_delay)
        
        return delays
    
    async def human_type(self, driver, element, text: str) -> None:
        """人类化打字"""
        try:
            delays = self.get_human_typing_delays(text)
            
            for char, delay in zip(text, delays):
                await element.send_keys(char)
                await asyncio.sleep(delay)
                
                # 偶尔的删除重打（模拟打错字）
                if random.random() < 0.02:  # 2%概率
                    await asyncio.sleep(random.uniform(0.1, 0.3))
                    await element.send_keys('\b')  # 退格
                    await asyncio.sleep(random.uniform(0.1, 0.2))
                    await element.send_keys(char)  # 重新输入
                    
        except Exception as e:
            print(f"人类化打字失败: {e}")
    
    def calculate_reading_time(self, content: str) -> float:
        """计算内容阅读时间"""
        word_count = len(content.split())
        char_count = len(content)
        
        # 中文按字符计算，英文按单词计算
        if any('\u4e00' <= char <= '\u9fff' for char in content):
            # 中文内容
            reading_time = char_count / (self.reading_speed / 60)
        else:
            # 英文内容
            reading_time = word_count / (self.reading_speed / 60)
        
        # 添加理解时间
        comprehension_time = reading_time * random.uniform(0.2, 0.5)
        
        return reading_time + comprehension_time

# 全局实例
human_behavior = HumanBehavior()
