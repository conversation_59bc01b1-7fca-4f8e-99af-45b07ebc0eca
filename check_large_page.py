"""
检查大页面内容
验证是否包含真实的职位信息
"""

import asyncio
from boss_crawler import BOSSCrawler
from data_extractor import data_extractor

async def check_large_page():
    """检查大页面内容"""
    print("🔍 检查大页面内容是否包含职位信息")
    print("=" * 60)
    
    test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
    
    async with BOSSCrawler() as crawler:
        # 使用第一个BOSS绕过方法
        method = crawler.access_methods[0]  # _method_boss_wait_token
        print(f"使用方法: {method.__name__}")
        
        html_content = await method(test_url)
        
        if html_content and len(html_content) > 100000:
            print(f"✅ 获取大页面内容，长度: {len(html_content)}")
            
            # 保存页面用于分析
            with open("large_page_content.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("💾 大页面内容已保存到 large_page_content.html")
            
            # 尝试提取职位信息
            print("\n🔍 尝试提取职位信息:")
            print("-" * 40)
            
            try:
                job_data = data_extractor.extract_job_details(html_content, test_url)

                if job_data:
                    print("✅ 成功提取职位信息:")
                    for key, value in job_data.items():
                        if value and value.strip():
                            print(f"  {key}: {value}")
                        else:
                            print(f"  {key}: (空)")
                else:
                    print("❌ 未能提取到职位信息")
                    
            except Exception as e:
                print(f"❌ 提取职位信息异常: {str(e)}")
            
            # 手动检查关键内容
            print("\n🔍 手动检查页面内容:")
            print("-" * 40)
            
            # 检查职位相关关键词
            job_keywords = [
                "职位", "薪资", "工作", "公司", "岗位", "招聘", 
                "面试", "简历", "待遇", "福利", "要求", "经验"
            ]
            
            found_keywords = []
            for keyword in job_keywords:
                if keyword in html_content:
                    count = html_content.count(keyword)
                    found_keywords.append(f"{keyword}({count})")
            
            if found_keywords:
                print(f"📋 发现职位关键词: {', '.join(found_keywords)}")
            else:
                print("❌ 未发现职位关键词")
            
            # 检查是否还有安全检查
            security_keywords = [
                "安全验证", "security", "验证码", "captcha", 
                "请稍候", "正在加载", "bot detection"
            ]
            
            security_found = []
            for keyword in security_keywords:
                if keyword.lower() in html_content.lower():
                    security_found.append(keyword)
            
            if security_found:
                print(f"⚠️  仍有安全检查痕迹: {', '.join(security_found)}")
            else:
                print("✅ 无安全检查痕迹")
            
            # 检查页面结构
            if "<title>" in html_content:
                import re
                title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
                if title_match:
                    title = title_match.group(1).strip()
                    print(f"📄 页面标题: {title}")
            
            # 检查是否有职位详情结构
            detail_indicators = [
                "job-detail", "position-detail", "职位详情", 
                "job-info", "position-info", "工作内容"
            ]
            
            structure_found = []
            for indicator in detail_indicators:
                if indicator in html_content:
                    structure_found.append(indicator)
            
            if structure_found:
                print(f"🏗️  发现职位详情结构: {', '.join(structure_found)}")
            else:
                print("❌ 未发现职位详情结构")
            
            # 显示页面开头和结尾
            print(f"\n📝 页面开头内容:")
            print("-" * 40)
            preview_start = html_content[:500].replace('\n', ' ').replace('\r', ' ')
            preview_start = ' '.join(preview_start.split())
            print(preview_start + "...")
            
            print(f"\n📝 页面结尾内容:")
            print("-" * 40)
            preview_end = html_content[-500:].replace('\n', ' ').replace('\r', ' ')
            preview_end = ' '.join(preview_end.split())
            print("..." + preview_end)
            
        else:
            print("❌ 未获取到大页面内容")

if __name__ == "__main__":
    asyncio.run(check_large_page())
