"""
FlareSolverr集成引擎
专门绕过Cloudflare和DDoS-GUARD保护
"""

import asyncio
import aiohttp
import json
import random
import time
from typing import Dict, Any, Optional

class FlareSolverrEngine:
    """FlareSolverr引擎 - 专门绕过Cloudflare"""
    
    def __init__(self, flaresolverr_url: str = "http://localhost:8191"):
        self.flaresolverr_url = flaresolverr_url
        self.session_id = None
        self.session_created = False
        
    async def check_flaresolverr_status(self) -> bool:
        """检查FlareSolverr服务状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.flaresolverr_url}/v1", timeout=5) as response:
                    return response.status == 200
        except:
            return False
    
    async def create_session(self) -> bool:
        """创建FlareSolverr会话"""
        try:
            data = {
                "cmd": "sessions.create",
                "session": f"boss_crawler_{int(time.time())}_{random.randint(1000, 9999)}"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.flaresolverr_url}/v1",
                    json=data,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("status") == "ok":
                            self.session_id = data["session"]
                            self.session_created = True
                            print(f"✅ FlareSolverr会话创建成功: {self.session_id}")
                            return True
            
            print("❌ FlareSolverr会话创建失败")
            return False
            
        except Exception as e:
            print(f"❌ FlareSolverr会话创建异常: {str(e)}")
            return False
    
    async def destroy_session(self):
        """销毁FlareSolverr会话"""
        if not self.session_created or not self.session_id:
            return
        
        try:
            data = {
                "cmd": "sessions.destroy",
                "session": self.session_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.flaresolverr_url}/v1",
                    json=data,
                    timeout=10
                ) as response:
                    if response.status == 200:
                        print(f"✅ FlareSolverr会话已销毁: {self.session_id}")
                    
            self.session_created = False
            self.session_id = None
            
        except Exception as e:
            print(f"❌ FlareSolverr会话销毁异常: {str(e)}")
    
    async def solve_cloudflare(self, url: str, max_timeout: int = 60000) -> Optional[str]:
        """使用FlareSolverr绕过Cloudflare"""
        try:
            print(f"🚀 使用FlareSolverr绕过Cloudflare: {url}")
            
            # 检查服务状态
            if not await self.check_flaresolverr_status():
                print("❌ FlareSolverr服务不可用")
                return None
            
            # 创建会话（如果还没有）
            if not self.session_created:
                if not await self.create_session():
                    return None
            
            # 构建请求数据
            data = {
                "cmd": "request.get",
                "url": url,
                "maxTimeout": max_timeout,
                "session": self.session_id,
                "returnOnlyCookies": False
            }
            
            print(f"发送FlareSolverr请求...")
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.flaresolverr_url}/v1",
                    json=data,
                    timeout=max_timeout / 1000 + 10  # 转换为秒并添加缓冲
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        elapsed_time = time.time() - start_time
                        
                        if result.get("status") == "ok":
                            solution = result.get("solution", {})
                            html_content = solution.get("response", "")
                            status_code = solution.get("status", 0)
                            
                            print(f"✅ FlareSolverr成功！")
                            print(f"📊 状态码: {status_code}")
                            print(f"📊 页面长度: {len(html_content)}")
                            print(f"⏱️  耗时: {elapsed_time:.2f} 秒")
                            
                            return html_content
                        else:
                            error_msg = result.get("message", "未知错误")
                            print(f"❌ FlareSolverr失败: {error_msg}")
                            return None
                    else:
                        print(f"❌ FlareSolverr HTTP错误: {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            print(f"❌ FlareSolverr超时")
            return None
        except Exception as e:
            print(f"❌ FlareSolverr异常: {str(e)}")
            return None
    
    async def solve_with_proxy(self, url: str, proxy_url: str, max_timeout: int = 60000) -> Optional[str]:
        """使用代理的FlareSolverr绕过"""
        try:
            print(f"🚀 使用FlareSolverr+代理绕过: {url}")
            
            # 检查服务状态
            if not await self.check_flaresolverr_status():
                print("❌ FlareSolverr服务不可用")
                return None
            
            # 创建带代理的会话
            session_data = {
                "cmd": "sessions.create",
                "session": f"boss_crawler_proxy_{int(time.time())}_{random.randint(1000, 9999)}",
                "proxy": {"url": proxy_url}
            }
            
            async with aiohttp.ClientSession() as session:
                # 创建代理会话
                async with session.post(
                    f"{self.flaresolverr_url}/v1",
                    json=session_data,
                    timeout=30
                ) as response:
                    if response.status != 200:
                        print("❌ 代理会话创建失败")
                        return None
                    
                    result = await response.json()
                    if result.get("status") != "ok":
                        print("❌ 代理会话创建失败")
                        return None
                    
                    proxy_session_id = session_data["session"]
                    print(f"✅ 代理会话创建成功: {proxy_session_id}")
                
                try:
                    # 使用代理会话请求
                    request_data = {
                        "cmd": "request.get",
                        "url": url,
                        "maxTimeout": max_timeout,
                        "session": proxy_session_id,
                        "returnOnlyCookies": False
                    }
                    
                    async with session.post(
                        f"{self.flaresolverr_url}/v1",
                        json=request_data,
                        timeout=max_timeout / 1000 + 10
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()
                            
                            if result.get("status") == "ok":
                                solution = result.get("solution", {})
                                html_content = solution.get("response", "")
                                print(f"✅ FlareSolverr+代理成功！页面长度: {len(html_content)}")
                                return html_content
                            else:
                                print(f"❌ FlareSolverr+代理失败: {result.get('message', '未知错误')}")
                                return None
                        else:
                            print(f"❌ FlareSolverr+代理HTTP错误: {response.status}")
                            return None
                
                finally:
                    # 销毁代理会话
                    destroy_data = {
                        "cmd": "sessions.destroy",
                        "session": proxy_session_id
                    }
                    try:
                        await session.post(f"{self.flaresolverr_url}/v1", json=destroy_data, timeout=10)
                        print(f"✅ 代理会话已销毁: {proxy_session_id}")
                    except:
                        pass
                        
        except Exception as e:
            print(f"❌ FlareSolverr+代理异常: {str(e)}")
            return None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.destroy_session()

# 全局实例
flaresolverr_engine = FlareSolverrEngine()
