"""
测试真实数据爬取
验证是否能够绕过反爬限制获取真实数据
"""

import asyncio
from boss_crawler import BOSSCrawler

async def test_real_job_crawling():
    """测试真实职位数据爬取"""
    print("🧪 测试真实职位数据爬取...")
    
    async with BOSSCrawler() as crawler:
        # 使用真实的职位URL
        test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
        
        print(f"🎯 测试URL: {test_url}")
        
        job_data = await crawler.crawl_job_detail(test_url)
        
        if job_data:
            print("\n✅ 真实数据爬取成功!")
            for key, value in job_data.items():
                if value:
                    print(f"   {key}: {str(value)[:100]}...")
            return True
        else:
            print("\n❌ 真实数据爬取失败")
            return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🔍 BOSS直聘真实数据爬取测试")
    print("=" * 60)
    
    success = await test_real_job_crawling()
    
    if success:
        print("🎉 测试通过！")
    else:
        print("❌ 测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
