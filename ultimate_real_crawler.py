"""
BOSS直聘终极真实数据爬虫
突破安全检查，获取真实职位数据
"""

import asyncio
import time
import random
import re
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler, BrowserConfig
from urllib.parse import urljoin, urlparse, parse_qs

from config import BASE_URL
from data_extractor import data_extractor
from data_processor import data_processor

class UltimateRealCrawler:
    """终极真实数据爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.crawler = None
        
        # 真实存在的职位URL
        self.real_job_urls = [
            "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        ]
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    def get_ultimate_browser_config(self) -> BrowserConfig:
        """获取终极浏览器配置"""
        config = BrowserConfig(
            headless=False,  # 使用有头模式，更真实
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--lang=zh-CN",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
        )
        return config
    
    def get_ultimate_crawl_config(self) -> Dict[str, Any]:
        """获取终极爬取配置"""
        return {
            "word_count_threshold": 10,
            "css_selector": None,
            "screenshot": False,
            "wait_for": "networkidle",
            "timeout": 120000,  # 2分钟超时
            "page_timeout": 240000,  # 4分钟页面超时
            "magic": True,
            "simulate_user": True,
            "override_navigator": True,
            "delay_before_return_html": 60.0,  # 等待1分钟让安全检查完成
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            },
            "js_code": [
                """
                // 终极反检测和安全检查绕过代码
                
                // 移除所有自动化标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                delete navigator.__proto__.webdriver;
                
                // 模拟真实浏览器环境
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            name: 'Chrome PDF Plugin',
                            filename: 'internal-pdf-viewer',
                            description: 'Portable Document Format'
                        },
                        {
                            name: 'Chrome PDF Viewer', 
                            filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                            description: 'Portable Document Format'
                        },
                        {
                            name: 'Native Client',
                            filename: 'internal-nacl-plugin',
                            description: 'Native Client'
                        }
                    ],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                });
                
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });
                
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                });
                
                // 模拟真实用户行为
                const simulateRealUserBehavior = async () => {
                    // 随机鼠标移动
                    const mouseMoveEvent = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(mouseMoveEvent);
                    
                    // 随机点击
                    const clickEvent = new MouseEvent('click', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(clickEvent);
                    
                    // 随机滚动
                    const scrollAmount = Math.random() * 500;
                    window.scrollTo({
                        top: scrollAmount,
                        behavior: 'smooth'
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
                    
                    // 滚回顶部
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                };
                
                // 等待页面完全加载
                if (document.readyState !== 'complete') {
                    await new Promise(resolve => {
                        if (document.readyState === 'complete') {
                            resolve();
                        } else {
                            window.addEventListener('load', resolve);
                        }
                    });
                }
                
                console.log('页面加载完成，开始安全检查处理...');
                
                // 初始等待
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // 执行用户行为模拟
                await simulateRealUserBehavior();
                
                // 检查并处理安全检查页面
                let securityCheckCount = 0;
                const maxSecurityChecks = 30;  // 增加最大检查次数
                
                while (securityCheckCount < maxSecurityChecks) {
                    const isSecurityPage = document.title === '请稍候' || 
                                         document.body.innerText.includes('请稍候') ||
                                         document.body.innerText.includes('正在加载中') ||
                                         document.body.innerText.includes('安全验证') ||
                                         document.querySelector('.boss-loading');
                    
                    if (!isSecurityPage) {
                        console.log('安全检查通过，页面加载完成');
                        break;
                    }
                    
                    console.log(`安全检查中... (${securityCheckCount + 1}/${maxSecurityChecks})`);
                    console.log('当前页面标题:', document.title);
                    console.log('页面内容长度:', document.body.innerHTML.length);
                    
                    // 在安全检查期间持续模拟用户行为
                    await simulateRealUserBehavior();
                    
                    // 尝试触发安全检查的完成
                    if (window.location.href.includes('security-check')) {
                        console.log('检测到安全检查页面，尝试处理...');
                        
                        // 等待安全检查脚本执行
                        await new Promise(resolve => setTimeout(resolve, 10000));
                        
                        // 检查是否有重定向
                        if (document.querySelector('script[src*="security-js"]')) {
                            console.log('检测到安全检查脚本，等待执行...');
                            await new Promise(resolve => setTimeout(resolve, 15000));
                        }
                    }
                    
                    // 等待更长时间
                    await new Promise(resolve => setTimeout(resolve, 8000 + Math.random() * 5000));
                    
                    securityCheckCount++;
                }
                
                // 最终等待确保页面完全加载
                await new Promise(resolve => setTimeout(resolve, 10000));
                
                console.log('最终页面处理完成');
                console.log('最终页面标题:', document.title);
                console.log('最终页面内容长度:', document.body.innerHTML.length);
                console.log('最终URL:', window.location.href);
                """
            ]
        }
    
    async def start(self):
        """启动爬虫"""
        browser_config = self.get_ultimate_browser_config()
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()
        print("终极真实数据爬虫已启动")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.close()
        print("终极真实数据爬虫已关闭")
    
    async def crawl_real_job_data(self, job_url: str) -> Dict[str, str]:
        """爬取真实职位数据"""
        print(f"\n🎯 开始爬取真实数据: {job_url}")
        
        try:
            # 随机延时
            await asyncio.sleep(random.uniform(5.0, 10.0))
            
            crawl_config = self.get_ultimate_crawl_config()
            
            print("正在访问页面，等待安全检查完成...")
            result = await self.crawler.arun(url=job_url, **crawl_config)
            
            if not result.success:
                print(f"❌ 页面访问失败: {result.error_message}")
                return None
            
            print(f"📄 获取页面内容，长度: {len(result.html)}")
            
            # 保存页面内容用于分析
            with open("final_page_content.html", "w", encoding="utf-8") as f:
                f.write(result.html)
            
            # 检查是否成功绕过安全检查
            is_security_page = (
                "请稍候" in result.html or 
                "正在加载中" in result.html or
                "安全验证" in result.html or
                "bossLoading" in result.html
            )
            
            if is_security_page:
                print("⚠️ 仍然是安全检查页面")
                
                # 分析页面内容，看是否有其他信息
                if len(result.html) > 15000:  # 如果页面内容较多，可能包含有用信息
                    print("📋 页面内容较多，尝试提取可用信息...")
                    job_data = data_extractor.extract_job_details(result.html, job_url)
                    
                    if job_data:
                        valid_fields = sum(1 for v in job_data.values() if v and len(str(v).strip()) > 0)
                        if valid_fields >= 1:
                            print(f"✅ 从安全检查页面提取到部分数据 ({valid_fields}/7 字段)")
                            return job_data
                
                print("❌ 无法从安全检查页面提取有效数据")
                return None
            else:
                print("✅ 成功绕过安全检查!")
                
                # 尝试提取职位数据
                job_data = data_extractor.extract_job_details(result.html, job_url)
                
                if job_data:
                    valid_fields = sum(1 for v in job_data.values() if v and len(str(v).strip()) > 0)
                    if valid_fields >= 1:
                        print(f"✅ 成功提取真实数据 ({valid_fields}/7 字段)")
                        return job_data
                
                print("⚠️ 绕过安全检查但数据提取失败")
                return None
                
        except Exception as e:
            print(f"❌ 爬取异常: {str(e)}")
            return None
    
    async def run_real_data_crawl(self, max_jobs: int = 5) -> Dict[str, str]:
        """运行真实数据爬取"""
        print("=" * 70)
        print("🚀 BOSS直聘终极真实数据爬虫启动")
        print("💪 使用最强技术突破安全检查")
        print("📋 严格要求: 只获取真实网址的真实数据")
        print(f"🎯 目标: 获取 {max_jobs} 个真实职位数据")
        print("=" * 70)
        
        start_time = time.time()
        
        # 使用真实URL
        target_urls = self.real_job_urls[:max_jobs]
        
        print(f"📋 准备爬取 {len(target_urls)} 个真实职位URL")
        
        # 串行爬取真实数据
        valid_jobs = []
        for i, url in enumerate(target_urls, 1):
            print(f"\n进度: {i}/{len(target_urls)}")
            job_data = await self.crawl_real_job_data(url)
            
            if job_data:
                valid_jobs.append(job_data)
                print(f"✅ 当前成功: {len(valid_jobs)} 个真实数据")
            
            # 在每次请求之间增加延时
            if i < len(target_urls):
                await asyncio.sleep(random.uniform(15.0, 25.0))
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n" + "=" * 70)
        print("🎉 终极真实数据爬取完成！")
        print(f"⏱️ 总耗时: {total_time:.2f} 秒")
        print(f"📊 成功获取: {len(valid_jobs)} 条真实数据")
        print(f"📈 成功率: {len(valid_jobs)}/{len(target_urls)} ({len(valid_jobs)/len(target_urls)*100:.1f}%)")
        
        if valid_jobs:
            # 处理和保存真实数据
            file_paths = data_processor.process_and_save(valid_jobs)
            print(f"📄 JSON文件: {file_paths['json']}")
            print(f"📊 Excel文件: {file_paths['excel']}")
            
            # 显示真实数据样例
            print(f"\n📋 真实数据样例:")
            for i, job in enumerate(valid_jobs[:3], 1):
                print(f"  {i}. {job['岗位名称']} - {job['薪资情况']} - {job['工作地点']}")
            
            return file_paths
        else:
            print("❌ 没有获取到真实数据")
            return {"json": "", "excel": ""}

async def main():
    """主函数"""
    crawler = UltimateRealCrawler()
    async with crawler:
        result = await crawler.run_real_data_crawl(max_jobs=1)
        return result

if __name__ == "__main__":
    asyncio.run(main())
