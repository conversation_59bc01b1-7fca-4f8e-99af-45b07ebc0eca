"""
系统健康检查器
检查系统各组件状态和依赖
"""

import asyncio
import sys
import importlib
import subprocess
from typing import Dict, List, Any
from logger_manager import logger

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.health_status = {}
        
    async def run_full_health_check(self) -> Dict[str, Any]:
        """运行完整健康检查"""
        logger.info("🔍 开始系统健康检查")
        
        health_report = {
            "overall_status": "unknown",
            "python_environment": await self._check_python_environment(),
            "dependencies": await self._check_dependencies(),
            "core_modules": await self._check_core_modules(),
            "network_connectivity": await self._check_network_connectivity(),
            "file_permissions": await self._check_file_permissions(),
            "recommendations": []
        }
        
        # 计算总体状态
        health_report["overall_status"] = self._calculate_overall_status(health_report)
        health_report["recommendations"] = self._generate_recommendations(health_report)
        
        logger.success("✅ 系统健康检查完成")
        return health_report
    
    async def _check_python_environment(self) -> Dict[str, Any]:
        """检查Python环境"""
        try:
            python_version = sys.version
            python_major = sys.version_info.major
            python_minor = sys.version_info.minor
            
            status = "healthy"
            issues = []
            
            if python_major < 3:
                status = "critical"
                issues.append("Python版本过低，需要Python 3.7+")
            elif python_major == 3 and python_minor < 7:
                status = "warning"
                issues.append("Python版本较低，建议升级到3.8+")
            
            return {
                "status": status,
                "version": python_version.split()[0],
                "major": python_major,
                "minor": python_minor,
                "issues": issues
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "issues": ["无法检查Python环境"]
            }
    
    async def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖包"""
        required_packages = [
            "crawl4ai", "beautifulsoup4", "pandas", "openpyxl", 
            "lxml", "aiohttp", "curl_cffi", "DrissionPage", "fake_useragent"
        ]
        
        results = {}
        missing_packages = []
        
        for package in required_packages:
            try:
                importlib.import_module(package.replace("-", "_"))
                results[package] = {"status": "installed", "version": "unknown"}
            except ImportError:
                results[package] = {"status": "missing"}
                missing_packages.append(package)
        
        overall_status = "healthy" if not missing_packages else "critical"
        
        return {
            "status": overall_status,
            "packages": results,
            "missing_packages": missing_packages,
            "total_required": len(required_packages),
            "installed_count": len(required_packages) - len(missing_packages)
        }
    
    async def _check_core_modules(self) -> Dict[str, Any]:
        """检查核心模块"""
        core_modules = [
            "boss_crawler", "boss_security_bypass", "ultimate_data_extractor",
            "smart_url_collector", "performance_optimizer", "workflow_manager",
            "advanced_crawler", "stats_analyzer", "config_manager", "logger_manager"
        ]
        
        results = {}
        failed_modules = []
        
        for module in core_modules:
            try:
                importlib.import_module(module)
                results[module] = {"status": "loaded"}
            except ImportError as e:
                results[module] = {"status": "failed", "error": str(e)}
                failed_modules.append(module)
        
        overall_status = "healthy" if not failed_modules else "critical"
        
        return {
            "status": overall_status,
            "modules": results,
            "failed_modules": failed_modules,
            "total_modules": len(core_modules),
            "loaded_count": len(core_modules) - len(failed_modules)
        }
    
    async def _check_network_connectivity(self) -> Dict[str, Any]:
        """检查网络连接"""
        test_urls = [
            "https://www.google.com",
            "https://www.zhipin.com"
        ]
        
        results = {}
        
        for url in test_urls:
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            results[url] = {"status": "accessible", "status_code": response.status}
                        else:
                            results[url] = {"status": "error", "status_code": response.status}
            except Exception as e:
                results[url] = {"status": "failed", "error": str(e)}
        
        accessible_count = sum(1 for r in results.values() if r["status"] == "accessible")
        overall_status = "healthy" if accessible_count == len(test_urls) else "warning"
        
        return {
            "status": overall_status,
            "urls": results,
            "accessible_count": accessible_count,
            "total_urls": len(test_urls)
        }
    
    async def _check_file_permissions(self) -> Dict[str, Any]:
        """检查文件权限"""
        import os
        
        test_dirs = ["output", "logs"]
        results = {}
        
        for dir_name in test_dirs:
            try:
                # 检查目录是否存在
                if not os.path.exists(dir_name):
                    os.makedirs(dir_name, exist_ok=True)
                
                # 检查写权限
                test_file = os.path.join(dir_name, "test_write.tmp")
                with open(test_file, "w") as f:
                    f.write("test")
                os.remove(test_file)
                
                results[dir_name] = {"status": "writable"}
                
            except Exception as e:
                results[dir_name] = {"status": "error", "error": str(e)}
        
        error_count = sum(1 for r in results.values() if r["status"] == "error")
        overall_status = "healthy" if error_count == 0 else "warning"
        
        return {
            "status": overall_status,
            "directories": results,
            "error_count": error_count
        }
    
    def _calculate_overall_status(self, health_report: Dict[str, Any]) -> str:
        """计算总体健康状态"""
        critical_issues = 0
        warning_issues = 0
        
        for component, data in health_report.items():
            if isinstance(data, dict) and "status" in data:
                if data["status"] == "critical":
                    critical_issues += 1
                elif data["status"] in ["warning", "error"]:
                    warning_issues += 1
        
        if critical_issues > 0:
            return "critical"
        elif warning_issues > 0:
            return "warning"
        else:
            return "healthy"
    
    def _generate_recommendations(self, health_report: Dict[str, Any]) -> List[str]:
        """生成健康建议"""
        recommendations = []
        
        # Python环境建议
        python_env = health_report.get("python_environment", {})
        if python_env.get("status") == "warning":
            recommendations.append("建议升级Python到3.8或更高版本")
        
        # 依赖包建议
        dependencies = health_report.get("dependencies", {})
        missing_packages = dependencies.get("missing_packages", [])
        if missing_packages:
            recommendations.append(f"安装缺失的依赖包: pip install {' '.join(missing_packages)}")
        
        # 核心模块建议
        core_modules = health_report.get("core_modules", {})
        failed_modules = core_modules.get("failed_modules", [])
        if failed_modules:
            recommendations.append(f"修复失败的核心模块: {', '.join(failed_modules)}")
        
        # 网络连接建议
        network = health_report.get("network_connectivity", {})
        if network.get("status") != "healthy":
            recommendations.append("检查网络连接，确保能访问目标网站")
        
        # 文件权限建议
        file_perms = health_report.get("file_permissions", {})
        if file_perms.get("error_count", 0) > 0:
            recommendations.append("检查文件权限，确保能创建输出文件")
        
        if not recommendations:
            recommendations.append("系统健康状态良好，无需特殊操作")
        
        return recommendations
    
    def print_health_report(self, health_report: Dict[str, Any]):
        """打印健康报告"""
        print("\n🏥 系统健康检查报告")
        print("=" * 60)
        
        # 总体状态
        overall = health_report["overall_status"]
        status_emoji = "🟢" if overall == "healthy" else "🟡" if overall == "warning" else "🔴"
        print(f"📊 总体状态: {status_emoji} {overall.upper()}")
        
        # 各组件状态
        components = [
            ("Python环境", "python_environment"),
            ("依赖包", "dependencies"), 
            ("核心模块", "core_modules"),
            ("网络连接", "network_connectivity"),
            ("文件权限", "file_permissions")
        ]
        
        print(f"\n🔍 组件检查:")
        for name, key in components:
            data = health_report.get(key, {})
            status = data.get("status", "unknown")
            emoji = "✅" if status == "healthy" else "⚠️" if status == "warning" else "❌"
            print(f"  {emoji} {name}: {status}")
            
            # 显示详细信息
            if key == "dependencies":
                missing = data.get("missing_packages", [])
                if missing:
                    print(f"    缺失包: {', '.join(missing)}")
            elif key == "core_modules":
                failed = data.get("failed_modules", [])
                if failed:
                    print(f"    失败模块: {', '.join(failed)}")
        
        # 建议
        recommendations = health_report.get("recommendations", [])
        if recommendations:
            print(f"\n💡 建议:")
            for rec in recommendations:
                print(f"  • {rec}")

# 全局系统健康检查器实例
system_health_checker = SystemHealthChecker()
