"""
BOSS直聘超高性能全站爬虫
深度优化反反爬机制，极致提升数据准确性
"""

import asyncio
import time
import random
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler, BrowserConfig

from config import BASE_URL
from data_extractor import data_extractor
from ultimate_data_extractor import ultimate_data_extractor
from data_processor import data_processor

# 导入BOSS安全检查绕过器
try:
    from boss_security_bypass import boss_security_bypass
    BOSS_SECURITY_BYPASS_AVAILABLE = True
    print("🚀 BOSS安全检查绕过器已加载")
except ImportError as e:
    print(f"❌ BOSS安全检查绕过器导入失败: {e}")
    BOSS_SECURITY_BYPASS_AVAILABLE = False

class BOSSCrawler:
    """BOSS直聘超高性能爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.crawler = None
        self.semaphore = asyncio.Semaphore(3)
        
        # 已知职位URL（真实存在的职位）
        self.known_job_urls = [
            "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        ]

        # 尝试不同的访问策略（新的高级方法优先）
        self.access_methods = []

        # 添加BOSS安全检查绕过方法（最高优先级）
        if BOSS_SECURITY_BYPASS_AVAILABLE:
            self.access_methods.append(self._method_boss_wait_token)
            self.access_methods.append(self._method_boss_simulate_flow)
            self.access_methods.append(self._method_boss_direct_bypass)

        # 保留原有方法作为备选
        self.access_methods.extend([
            self._method_direct_access,
            self._method_with_referrer,
            self._method_mobile_ua,
            self._method_api_approach
        ])
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    def get_browser_config(self) -> BrowserConfig:
        """获取终极反反爬浏览器配置"""
        config = BrowserConfig(
            headless=False,  # 改为有头模式，更难被检测
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                # 最小化反检测参数
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8",

                # 深度反检测
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",

                # 模拟真实用户环境
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--force-device-scale-factor=1",
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            },
            cookies=[
                {
                    "name": "__zp_stoken__",
                    "value": f"token_{random.randint(100000000, 999999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                },
                {
                    "name": "__c",
                    "value": f"c_{random.randint(1000000000, 9999999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                }
            ]
        )
        return config
    
    def get_crawl_config(self) -> Dict[str, Any]:
        """获取终极反反爬爬取配置"""
        return {
            "word_count_threshold": 10,
            "css_selector": None,
            "screenshot": False,
            "wait_for": "domcontentloaded",  # 改为DOM加载完成
            "timeout": 90000,  # 增加超时时间
            "page_timeout": 180000,  # 3分钟页面超时
            "magic": True,
            "simulate_user": True,
            "override_navigator": True,
            "delay_before_return_html": 30.0,  # 大幅增加等待时间
            "headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            },
            "js_code": [
                """
                // 终极反检测JS代码

                // 移除所有自动化标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 删除自动化相关属性
                delete navigator.__proto__.webdriver;

                // 模拟真实浏览器环境
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            name: 'Chrome PDF Plugin',
                            filename: 'internal-pdf-viewer',
                            description: 'Portable Document Format'
                        },
                        {
                            name: 'Chrome PDF Viewer',
                            filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                            description: 'Portable Document Format'
                        },
                        {
                            name: 'Native Client',
                            filename: 'internal-nacl-plugin',
                            description: 'Native Client'
                        }
                    ],
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                });

                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });

                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 8,
                });

                // 模拟真实用户行为
                const simulateRealUser = async () => {
                    // 随机鼠标移动
                    const mouseMoveEvent = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(mouseMoveEvent);

                    // 随机滚动
                    const scrollAmount = Math.random() * 300;
                    window.scrollTo({
                        top: scrollAmount,
                        behavior: 'smooth'
                    });

                    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                    // 滚回顶部
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });

                    await new Promise(resolve => setTimeout(resolve, 1000));
                };

                // 等待页面完全加载
                if (document.readyState !== 'complete') {
                    await new Promise(resolve => {
                        if (document.readyState === 'complete') {
                            resolve();
                        } else {
                            window.addEventListener('load', resolve);
                        }
                    });
                }

                // 初始等待
                await new Promise(resolve => setTimeout(resolve, 3000));

                // 执行用户行为模拟
                await simulateRealUser();

                // 检查并等待安全检查页面
                let securityCheckCount = 0;
                const maxSecurityChecks = 20;

                while (securityCheckCount < maxSecurityChecks) {
                    const isSecurityPage = document.title === '请稍候' ||
                                         document.body.innerText.includes('请稍候') ||
                                         document.body.innerText.includes('正在加载中') ||
                                         document.body.innerText.includes('安全验证');

                    if (!isSecurityPage) {
                        console.log('安全检查通过，页面加载完成');
                        break;
                    }

                    console.log(`安全检查中... (${securityCheckCount + 1}/${maxSecurityChecks})`);

                    // 在安全检查期间模拟用户行为
                    await simulateRealUser();

                    // 等待更长时间
                    await new Promise(resolve => setTimeout(resolve, 5000 + Math.random() * 3000));

                    securityCheckCount++;
                }

                // 最终等待确保页面完全加载
                await new Promise(resolve => setTimeout(resolve, 5000));

                console.log('页面处理完成，标题:', document.title);
                console.log('页面内容长度:', document.body.innerHTML.length);
                """
            ]
        }
    
    async def start(self):
        """启动爬虫"""
        browser_config = self.get_browser_config()
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()
        print("BOSS直聘爬虫已启动")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.close()
        print("BOSS直聘爬虫已关闭")
    
    def generate_job_urls(self, count: int) -> List[str]:
        """生成可能的职位URL"""
        urls = self.known_job_urls.copy()
        
        for i in range(count - len(urls)):
            random_id = f"{''.join(random.choices('abcdef0123456789', k=16))}1X{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=8))}"
            url = f"https://www.zhipin.com/job_detail/{random_id}.html"
            urls.append(url)
        
        return urls[:count]
    
    async def _method_direct_access(self, job_url: str) -> str:
        """方法1: 直接访问"""
        try:
            crawl_config = self.get_crawl_config()
            result = await self.crawler.arun(url=job_url, **crawl_config)

            if result.success:
                print(f"直接访问成功，页面长度: {len(result.html)}")
                return result.html
            else:
                print(f"直接访问失败: {result.error_message}")
                return ""
        except Exception as e:
            print(f"直接访问异常: {str(e)}")
            return ""

    async def _method_with_referrer(self, job_url: str) -> str:
        """方法2: 带Referrer访问"""
        try:
            config = self.get_crawl_config()
            if "headers" not in config:
                config["headers"] = {}
            config["headers"]["Referer"] = "https://www.zhipin.com/"

            result = await self.crawler.arun(url=job_url, **config)

            if result.success and len(result.html) > 10000:
                return result.html
            return ""
        except Exception as e:
            print(f"带Referrer访问失败: {str(e)}")
            return ""

    async def _method_mobile_ua(self, job_url: str) -> str:
        """方法3: 移动端User-Agent"""
        try:
            config = self.get_crawl_config()
            if "headers" not in config:
                config["headers"] = {}
            config["user_agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            config["headers"]["User-Agent"] = config["user_agent"]

            result = await self.crawler.arun(url=job_url, **config)

            if result.success and len(result.html) > 10000:
                return result.html
            return ""
        except Exception as e:
            print(f"移动端访问失败: {str(e)}")
            return ""

    async def _method_api_approach(self, job_url: str) -> str:
        """方法4: 尝试API方式"""
        try:
            # 从URL中提取job ID
            job_id = job_url.split('/')[-1].replace('.html', '')

            # 尝试API端点
            api_url = f"https://www.zhipin.com/wapi/zpgeek/job/detail.json?jobId={job_id}"

            config = self.get_crawl_config()
            if "headers" not in config:
                config["headers"] = {}
            config["headers"]["Accept"] = "application/json"

            result = await self.crawler.arun(url=api_url, **config)

            if result.success:
                return result.html  # 这里是JSON响应
            return ""
        except Exception as e:
            print(f"API方式失败: {str(e)}")
            return ""

    # ==================== BOSS安全检查绕过方法 ====================

    async def _method_boss_wait_token(self, job_url: str) -> str:
        """BOSS绕过方法1: 等待安全检查完成并提取token"""
        if not BOSS_SECURITY_BYPASS_AVAILABLE:
            return ""

        try:
            return await boss_security_bypass.method_wait_and_extract_token(job_url)
        except Exception as e:
            print(f"BOSS等待token方法失败: {str(e)}")
            return ""

    async def _method_boss_simulate_flow(self, job_url: str) -> str:
        """BOSS绕过方法2: 模拟安全检查流程"""
        if not BOSS_SECURITY_BYPASS_AVAILABLE:
            return ""

        try:
            return await boss_security_bypass.method_simulate_security_flow(job_url)
        except Exception as e:
            print(f"BOSS模拟流程方法失败: {str(e)}")
            return ""

    async def _method_boss_direct_bypass(self, job_url: str) -> str:
        """BOSS绕过方法3: 直接绕过安全检查"""
        if not BOSS_SECURITY_BYPASS_AVAILABLE:
            return ""

        try:
            return await boss_security_bypass.method_direct_bypass(job_url)
        except Exception as e:
            print(f"BOSS直接绕过方法失败: {str(e)}")
            return ""



    async def crawl_job_detail(self, job_url: str) -> Dict[str, str]:
        """爬取单个职位详情（多方法尝试）"""
        async with self.semaphore:
            print(f"\n🎯 开始爬取: {job_url}")

            # 随机延时
            await asyncio.sleep(random.uniform(3.0, 8.0))

            # 尝试不同的访问方法
            for i, method in enumerate(self.access_methods, 1):
                try:
                    print(f"尝试方法 {i}: {method.__name__}")

                    html_content = await method(job_url)

                    if html_content and len(html_content) > 500:  # 降低长度要求
                        print(f"方法 {i} 获取到内容，长度: {len(html_content)}")

                        # 保存页面内容用于调试
                        with open(f"debug_method_{i}.html", "w", encoding="utf-8") as f:
                            f.write(html_content)

                        # 检查是否是安全检查页面
                        is_security_page = (
                            "请稍候" in html_content or
                            "正在加载中" in html_content or
                            "安全验证" in html_content or
                            "bossLoading" in html_content
                        )

                        if not is_security_page:
                            print(f"方法 {i} 成功绕过安全检查")

                            # 保存成功的页面内容用于调试
                            with open(f"success_page_{i}.html", "w", encoding="utf-8") as f:
                                f.write(html_content)

                            # 使用终极数据提取器确保7个字段完整提取
                            job_data = ultimate_data_extractor.extract_job_details(html_content, job_url)

                            # 如果终极提取器结果不理想，使用备用提取器
                            if not job_data or sum(1 for v in job_data.values() if v and v.strip()) < 4:
                                print("🔄 终极提取器结果不理想，使用备用提取器...")
                                backup_data = data_extractor.extract_job_details(html_content, job_url)

                                # 合并结果，优先使用有内容的字段
                                if backup_data:
                                    for key in job_data:
                                        if not job_data[key] and backup_data.get(key):
                                            job_data[key] = backup_data[key]

                            if job_data:
                                valid_fields = sum(1 for v in job_data.values() if v and len(str(v).strip()) > 0)
                                if valid_fields >= 1:  # 至少有1个字段有数据
                                    print(f"✅ 方法 {i} 成功提取数据 ({valid_fields}/7 字段)")
                                    return job_data

                            print(f"方法 {i} 页面正常但数据提取失败")
                        else:
                            print(f"方法 {i} 仍然是安全检查页面")
                    else:
                        print(f"方法 {i} 未获取到有效内容")

                    # 在方法之间增加延时
                    if i < len(self.access_methods):
                        await asyncio.sleep(random.uniform(5.0, 10.0))

                except Exception as e:
                    print(f"方法 {i} 异常: {str(e)}")
                    continue

            print(f"❌ 所有方法都失败了: {job_url}")
            return None
    
    async def run_crawl(self, max_jobs: int = 10) -> Dict[str, str]:
        """运行爬取任务（深度优化版）"""
        print("=" * 60)
        print("BOSS直聘超高性能爬虫启动")
        print("🎯 深度优化反反爬机制")
        print("📋 严格要求: 只获取真实网址的真实数据")
        print(f"目标: 获取 {max_jobs} 个职位数据")
        print("=" * 60)

        start_time = time.time()

        # 使用真实存在的职位URL
        target_urls = self.known_job_urls[:max_jobs]

        # 如果需要更多URL，生成基于真实模式的URL
        while len(target_urls) < max_jobs:
            additional_urls = self.generate_job_urls(max_jobs - len(target_urls))
            target_urls.extend(additional_urls)

        target_urls = target_urls[:max_jobs]
        print(f"准备爬取 {len(target_urls)} 个真实职位URL")

        valid_jobs = []
        security_check_count = 0

        for i, url in enumerate(target_urls, 1):
            print(f"\n进度: {i}/{len(target_urls)}")
            job_data = await self.crawl_job_detail(url)

            if job_data:
                valid_jobs.append(job_data)
                print(f"✅ 当前成功: {len(valid_jobs)} 个真实数据")
            else:
                security_check_count += 1
                print(f"❌ 遇到安全检查或数据提取失败")

            # 增加请求间隔，避免触发更强的反爬
            if i < len(target_urls):
                await asyncio.sleep(random.uniform(10.0, 20.0))

        end_time = time.time()
        total_time = end_time - start_time

        print(f"\n" + "=" * 60)
        print("爬取任务完成！")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"成功获取: {len(valid_jobs)} 条真实数据")
        print(f"安全检查次数: {security_check_count}")
        print(f"成功率: {len(valid_jobs)}/{len(target_urls)} ({len(valid_jobs)/len(target_urls)*100:.1f}%)")

        if len(valid_jobs) == 0:
            print("\n⚠️ 重要提示:")
            print("所有访问都遇到了BOSS直聘的安全检查页面")
            print("这表明网站有非常强的反爬机制")
            print("建议:")
            print("1. 尝试使用真实浏览器手动访问验证")
            print("2. 考虑使用其他数据获取方式")
            print("3. 联系网站获取API访问权限")

        if valid_jobs:
            file_paths = data_processor.process_and_save(valid_jobs)
            print(f"📄 JSON文件: {file_paths['json']}")
            print(f"📊 Excel文件: {file_paths['excel']}")
            return file_paths
        else:
            return {"json": "", "excel": ""}

async def main():
    """主函数"""
    async with BOSSCrawler() as crawler:
        result = await crawler.run_crawl(max_jobs=10)
        return result

if __name__ == "__main__":
    asyncio.run(main())
