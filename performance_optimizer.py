"""
性能监控和优化器
实时监控爬虫性能并自动优化
"""

import time
import statistics
from typing import List, Dict, Any
from collections import deque
from config_manager import config_manager
from logger_manager import logger

class PerformanceOptimizer:
    """性能监控和优化器"""
    
    def __init__(self):
        self.performance_history = deque(maxlen=100)  # 保留最近100次记录
        self.method_performance = {}  # 各方法性能统计
        self.current_session = {
            "start_time": time.time(),
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_duration": 0,
            "field_extraction_stats": {
                "岗位名称": 0, "薪资情况": 0, "待遇情况": 0,
                "职位描述": 0, "公司简介": 0, "工作地点": 0, "实际网址": 0
            }
        }
    
    def record_request(self, method_name: str, url: str, duration: float, 
                      success: bool, extracted_fields: int = 0, job_data: Dict = None):
        """记录请求性能"""
        record = {
            "timestamp": time.time(),
            "method": method_name,
            "url": url,
            "duration": duration,
            "success": success,
            "extracted_fields": extracted_fields,
            "job_data": job_data
        }
        
        self.performance_history.append(record)
        
        # 更新会话统计
        self.current_session["total_requests"] += 1
        self.current_session["total_duration"] += duration
        
        if success:
            self.current_session["successful_requests"] += 1
            
            # 统计字段提取情况
            if job_data:
                for field, value in job_data.items():
                    if field in self.current_session["field_extraction_stats"] and value and value.strip():
                        self.current_session["field_extraction_stats"][field] += 1
        else:
            self.current_session["failed_requests"] += 1
        
        # 更新方法性能统计
        if method_name not in self.method_performance:
            self.method_performance[method_name] = {
                "total_requests": 0,
                "successful_requests": 0,
                "total_duration": 0,
                "avg_duration": 0,
                "success_rate": 0,
                "avg_fields": 0
            }
        
        method_stats = self.method_performance[method_name]
        method_stats["total_requests"] += 1
        method_stats["total_duration"] += duration
        
        if success:
            method_stats["successful_requests"] += 1
        
        # 更新平均值
        method_stats["avg_duration"] = method_stats["total_duration"] / method_stats["total_requests"]
        method_stats["success_rate"] = (method_stats["successful_requests"] / method_stats["total_requests"]) * 100
        
        logger.debug(f"性能记录: {method_name} | {duration:.2f}s | 成功: {success} | 字段: {extracted_fields}/7")
    
    def get_best_method(self) -> str:
        """获取当前表现最佳的方法"""
        if not self.method_performance:
            return None
        
        # 综合评分：成功率 * 0.6 + 速度分 * 0.4
        best_method = None
        best_score = 0
        
        for method, stats in self.method_performance.items():
            if stats["total_requests"] < 2:  # 样本太少
                continue
            
            success_score = stats["success_rate"]
            speed_score = max(0, 100 - (stats["avg_duration"] - 10) * 5)  # 10秒为基准
            
            total_score = success_score * 0.6 + speed_score * 0.4
            
            if total_score > best_score:
                best_score = total_score
                best_method = method
        
        return best_method
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.performance_history:
            return {"error": "没有性能数据"}
        
        # 基础统计
        total_requests = len(self.performance_history)
        successful_requests = sum(1 for r in self.performance_history if r["success"])
        success_rate = (successful_requests / total_requests) * 100
        
        durations = [r["duration"] for r in self.performance_history]
        avg_duration = statistics.mean(durations)
        min_duration = min(durations)
        max_duration = max(durations)
        
        # 字段提取统计
        field_stats = {}
        for field in ["岗位名称", "薪资情况", "待遇情况", "职位描述", "公司简介", "工作地点", "实际网址"]:
            field_count = 0
            for record in self.performance_history:
                if record["success"] and record["job_data"]:
                    if record["job_data"].get(field, "").strip():
                        field_count += 1
            
            field_stats[field] = {
                "count": field_count,
                "rate": (field_count / successful_requests) * 100 if successful_requests > 0 else 0
            }
        
        # 方法性能排名
        method_ranking = []
        for method, stats in self.method_performance.items():
            if stats["total_requests"] >= 1:
                method_ranking.append({
                    "method": method,
                    "success_rate": stats["success_rate"],
                    "avg_duration": stats["avg_duration"],
                    "total_requests": stats["total_requests"]
                })
        
        method_ranking.sort(key=lambda x: x["success_rate"], reverse=True)
        
        return {
            "session_stats": {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "success_rate": success_rate,
                "avg_duration": avg_duration,
                "min_duration": min_duration,
                "max_duration": max_duration
            },
            "field_extraction": field_stats,
            "method_ranking": method_ranking,
            "best_method": self.get_best_method(),
            "recommendations": self._get_recommendations()
        }
    
    def _get_recommendations(self) -> List[str]:
        """获取性能优化建议"""
        recommendations = []
        
        if not self.performance_history:
            return ["需要更多数据来分析性能"]
        
        # 分析成功率
        success_rate = (self.current_session["successful_requests"] / 
                       self.current_session["total_requests"]) * 100
        
        if success_rate < 80:
            recommendations.append("成功率偏低，建议检查网络连接和反反爬策略")
        elif success_rate >= 95:
            recommendations.append("成功率优秀，当前策略效果很好")
        
        # 分析速度
        if self.performance_history:
            avg_duration = statistics.mean([r["duration"] for r in self.performance_history])
            if avg_duration > 30:
                recommendations.append("平均响应时间较长，建议优化网络配置或减少等待时间")
            elif avg_duration < 15:
                recommendations.append("响应速度优秀，性能表现良好")
        
        # 分析字段提取
        field_rates = []
        for field, count in self.current_session["field_extraction_stats"].items():
            if self.current_session["successful_requests"] > 0:
                rate = (count / self.current_session["successful_requests"]) * 100
                field_rates.append(rate)
        
        if field_rates:
            avg_field_rate = statistics.mean(field_rates)
            if avg_field_rate < 80:
                recommendations.append("字段提取率偏低，建议优化数据提取器")
            elif avg_field_rate >= 95:
                recommendations.append("字段提取率优秀，数据质量很高")
        
        return recommendations
    
    def print_real_time_stats(self):
        """打印实时统计"""
        if self.current_session["total_requests"] == 0:
            print("📊 暂无性能数据")
            return
        
        success_rate = (self.current_session["successful_requests"] / 
                       self.current_session["total_requests"]) * 100
        
        avg_duration = (self.current_session["total_duration"] / 
                       self.current_session["total_requests"])
        
        print(f"📊 实时性能统计:")
        print(f"  请求总数: {self.current_session['total_requests']}")
        print(f"  成功率: {self.current_session['successful_requests']}/{self.current_session['total_requests']} ({success_rate:.1f}%)")
        print(f"  平均耗时: {avg_duration:.2f} 秒")
        
        # 字段提取统计
        if self.current_session["successful_requests"] > 0:
            print(f"  字段提取率:")
            for field, count in self.current_session["field_extraction_stats"].items():
                rate = (count / self.current_session["successful_requests"]) * 100
                print(f"    {field}: {rate:.1f}%")
    
    def optimize_config(self):
        """基于性能数据优化配置"""
        if len(self.performance_history) < 5:
            return
        
        # 分析最佳延时
        successful_records = [r for r in self.performance_history if r["success"]]
        if successful_records:
            avg_success_duration = statistics.mean([r["duration"] for r in successful_records])
            
            # 如果成功请求平均时间很短，可以减少延时
            if avg_success_duration < 15:
                current_delay = config_manager.get("crawler", "delay_between_requests", 2)
                if current_delay > 1:
                    new_delay = max(1, current_delay - 0.5)
                    config_manager.set("crawler", "delay_between_requests", new_delay)
                    logger.info(f"性能优化: 减少请求延时到 {new_delay} 秒")
        
        # 分析最佳超时时间
        timeout_records = [r for r in self.performance_history if r["duration"] > 25]
        if len(timeout_records) > len(self.performance_history) * 0.3:  # 超过30%的请求超时
            current_timeout = config_manager.get("crawler", "timeout", 30)
            new_timeout = min(60, current_timeout + 10)
            config_manager.set("crawler", "timeout", new_timeout)
            logger.info(f"性能优化: 增加超时时间到 {new_timeout} 秒")

# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()
