"""
统计分析器
分析爬取结果和性能数据
"""

import json
import pandas as pd
from typing import List, Dict, Any
from collections import Counter
import re
from datetime import datetime

class StatsAnalyzer:
    """统计分析器"""
    
    def __init__(self):
        pass
    
    def analyze_jobs_data(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """分析职位数据"""
        if not jobs_data:
            return {"error": "没有数据可分析"}
        
        stats = {
            "basic_stats": self._get_basic_stats(jobs_data),
            "salary_analysis": self._analyze_salary(jobs_data),
            "location_analysis": self._analyze_location(jobs_data),
            "job_title_analysis": self._analyze_job_titles(jobs_data),
            "data_quality": self._analyze_data_quality(jobs_data)
        }
        
        return stats
    
    def _get_basic_stats(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """基础统计"""
        return {
            "total_jobs": len(jobs_data),
            "analysis_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "fields_analyzed": list(jobs_data[0].keys()) if jobs_data else []
        }
    
    def _analyze_salary(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """薪资分析"""
        salaries = [job.get("薪资情况", "") for job in jobs_data if job.get("薪资情况")]
        
        if not salaries:
            return {"error": "没有薪资数据"}
        
        # 解析薪资范围
        salary_ranges = []
        salary_patterns = [
            r'(\d+)-(\d+)K',  # 10-15K
            r'(\d+)K-(\d+)K',  # 10K-15K
            r'(\d+)-(\d+)千',  # 10-15千
            r'(\d+)千-(\d+)千',  # 10千-15千
        ]
        
        for salary in salaries:
            for pattern in salary_patterns:
                match = re.search(pattern, salary)
                if match:
                    min_salary = int(match.group(1))
                    max_salary = int(match.group(2))
                    avg_salary = (min_salary + max_salary) / 2
                    salary_ranges.append({
                        "min": min_salary,
                        "max": max_salary,
                        "avg": avg_salary,
                        "original": salary
                    })
                    break
        
        if salary_ranges:
            avg_salaries = [s["avg"] for s in salary_ranges]
            return {
                "total_with_salary": len(salaries),
                "parsed_salaries": len(salary_ranges),
                "avg_salary": round(sum(avg_salaries) / len(avg_salaries), 1),
                "min_salary": min(s["min"] for s in salary_ranges),
                "max_salary": max(s["max"] for s in salary_ranges),
                "salary_distribution": self._get_salary_distribution(avg_salaries)
            }
        else:
            return {
                "total_with_salary": len(salaries),
                "parsed_salaries": 0,
                "note": "无法解析薪资格式"
            }
    
    def _get_salary_distribution(self, salaries: List[float]) -> Dict[str, int]:
        """薪资分布"""
        distribution = {
            "5K以下": 0,
            "5-10K": 0,
            "10-15K": 0,
            "15-20K": 0,
            "20-30K": 0,
            "30K以上": 0
        }
        
        for salary in salaries:
            if salary < 5:
                distribution["5K以下"] += 1
            elif salary < 10:
                distribution["5-10K"] += 1
            elif salary < 15:
                distribution["10-15K"] += 1
            elif salary < 20:
                distribution["15-20K"] += 1
            elif salary < 30:
                distribution["20-30K"] += 1
            else:
                distribution["30K以上"] += 1
        
        return distribution
    
    def _analyze_location(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """地点分析"""
        locations = [job.get("工作地点", "") for job in jobs_data if job.get("工作地点")]
        
        if not locations:
            return {"error": "没有地点数据"}
        
        # 提取城市信息
        cities = []
        for location in locations:
            # 简单的城市提取逻辑
            city_patterns = [
                r'(北京|上海|广州|深圳|杭州|南京|苏州|成都|武汉|西安|重庆|天津|青岛|大连|厦门|宁波|无锡|长沙|郑州|济南|福州|合肥|昆明|南昌|贵阳|太原|石家庄|哈尔滨|长春|沈阳|兰州|银川|西宁|乌鲁木齐|拉萨|呼和浩特|南宁|海口|三亚)',
                r'(\w+市)',
                r'(\w+区)',
            ]
            
            for pattern in city_patterns:
                match = re.search(pattern, location)
                if match:
                    cities.append(match.group(1))
                    break
            else:
                # 如果没有匹配到，取前几个字符作为地点
                cities.append(location[:10] if len(location) > 10 else location)
        
        city_counter = Counter(cities)
        
        return {
            "total_with_location": len(locations),
            "unique_locations": len(set(cities)),
            "top_cities": dict(city_counter.most_common(10)),
            "city_distribution": dict(city_counter)
        }
    
    def _analyze_job_titles(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """职位标题分析"""
        titles = [job.get("岗位名称", "") for job in jobs_data if job.get("岗位名称")]
        
        if not titles:
            return {"error": "没有职位标题数据"}
        
        # 提取关键词
        keywords = []
        for title in titles:
            # 简单的关键词提取
            words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
            keywords.extend(words)
        
        keyword_counter = Counter(keywords)
        
        return {
            "total_titles": len(titles),
            "unique_titles": len(set(titles)),
            "top_keywords": dict(keyword_counter.most_common(20)),
            "avg_title_length": round(sum(len(title) for title in titles) / len(titles), 1)
        }
    
    def _analyze_data_quality(self, jobs_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """数据质量分析"""
        total_jobs = len(jobs_data)
        field_completeness = {}
        
        for field in jobs_data[0].keys():
            filled_count = sum(1 for job in jobs_data if job.get(field, "").strip())
            completeness = (filled_count / total_jobs) * 100
            field_completeness[field] = {
                "filled": filled_count,
                "total": total_jobs,
                "completeness": round(completeness, 1)
            }
        
        # 计算整体数据质量分数
        avg_completeness = sum(data["completeness"] for data in field_completeness.values()) / len(field_completeness)
        
        return {
            "field_completeness": field_completeness,
            "overall_quality_score": round(avg_completeness, 1),
            "quality_grade": self._get_quality_grade(avg_completeness)
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """获取质量等级"""
        if score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "一般"
        elif score >= 60:
            return "及格"
        else:
            return "需改进"
    
    def generate_report(self, jobs_data: List[Dict[str, str]], output_file: str = None) -> str:
        """生成分析报告"""
        stats = self.analyze_jobs_data(jobs_data)
        
        report = []
        report.append("📊 BOSS直聘爬虫数据分析报告")
        report.append("=" * 60)
        
        # 基础统计
        basic = stats.get("basic_stats", {})
        report.append(f"📋 基础统计:")
        report.append(f"  总职位数: {basic.get('total_jobs', 0)}")
        report.append(f"  分析时间: {basic.get('analysis_time', 'N/A')}")
        report.append("")
        
        # 数据质量
        quality = stats.get("data_quality", {})
        if "overall_quality_score" in quality:
            report.append(f"🎯 数据质量:")
            report.append(f"  整体质量分数: {quality['overall_quality_score']}%")
            report.append(f"  质量等级: {quality['quality_grade']}")
            report.append(f"  字段完整度:")
            for field, data in quality.get("field_completeness", {}).items():
                report.append(f"    {field}: {data['completeness']}% ({data['filled']}/{data['total']})")
            report.append("")
        
        # 薪资分析
        salary = stats.get("salary_analysis", {})
        if "avg_salary" in salary:
            report.append(f"💰 薪资分析:")
            report.append(f"  平均薪资: {salary['avg_salary']}K")
            report.append(f"  薪资范围: {salary['min_salary']}K - {salary['max_salary']}K")
            report.append(f"  有薪资信息: {salary['total_with_salary']} 个职位")
            if "salary_distribution" in salary:
                report.append(f"  薪资分布:")
                for range_name, count in salary["salary_distribution"].items():
                    report.append(f"    {range_name}: {count} 个")
            report.append("")
        
        # 地点分析
        location = stats.get("location_analysis", {})
        if "top_cities" in location:
            report.append(f"📍 地点分析:")
            report.append(f"  有地点信息: {location['total_with_location']} 个职位")
            report.append(f"  热门城市:")
            for city, count in list(location["top_cities"].items())[:5]:
                report.append(f"    {city}: {count} 个")
            report.append("")
        
        # 职位分析
        job_title = stats.get("job_title_analysis", {})
        if "top_keywords" in job_title:
            report.append(f"📋 职位分析:")
            report.append(f"  职位总数: {job_title['total_titles']}")
            report.append(f"  独特职位: {job_title['unique_titles']}")
            report.append(f"  热门关键词:")
            for keyword, count in list(job_title["top_keywords"].items())[:10]:
                if len(keyword) > 1:  # 过滤单字符
                    report.append(f"    {keyword}: {count} 次")
            report.append("")
        
        report_text = "\n".join(report)
        
        # 保存报告
        if output_file:
            try:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(report_text)
                print(f"📊 分析报告已保存: {output_file}")
            except Exception as e:
                print(f"❌ 报告保存失败: {str(e)}")
        
        return report_text

# 全局统计分析器实例
stats_analyzer = StatsAnalyzer()
