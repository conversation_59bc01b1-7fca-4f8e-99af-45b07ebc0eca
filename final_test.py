"""
最终功能测试
验证简化后的爬虫系统
"""

import asyncio
import time
from boss_crawler import BOSSCrawler

async def final_test():
    """最终功能测试"""
    print("🎯 BOSS直聘爬虫系统 - 最终功能测试")
    print("=" * 60)
    print("🏆 目标：验证7个字段100%提取成功")
    print("🚀 技术：简化版高效爬虫系统")
    print("=" * 60)
    
    test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
    
    start_time = time.time()
    
    async with BOSSCrawler() as crawler:
        print(f"📍 测试URL: {test_url}")
        
        try:
            job_data = await crawler.crawl_job_detail(test_url)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if job_data:
                print(f"\n🎉 测试成功！")
                print(f"⏱️  总耗时: {duration:.2f} 秒")
                
                # 验证7个字段
                required_fields = [
                    "岗位名称", "薪资情况", "待遇情况", 
                    "职位描述", "公司简介", "工作地点", "实际网址"
                ]
                
                extracted_count = 0
                
                print(f"\n📊 7个字段验证结果:")
                print("=" * 50)
                
                for i, field in enumerate(required_fields, 1):
                    value = job_data.get(field, "")
                    
                    if value and value.strip():
                        extracted_count += 1
                        status = "✅"
                        length = len(value)
                        preview = value[:40] + "..." if len(value) > 40 else value
                    else:
                        status = "❌"
                        length = 0
                        preview = "(未提取到)"
                    
                    print(f"  {status} {i}. {field}: {preview}")
                
                # 最终评估
                success_rate = (extracted_count / len(required_fields)) * 100
                
                print(f"\n🏆 最终结果:")
                print("=" * 50)
                print(f"✅ 字段提取成功率: {extracted_count}/{len(required_fields)} ({success_rate:.1f}%)")
                print(f"⏱️  系统响应时间: {duration:.2f} 秒")
                
                if success_rate == 100:
                    print(f"\n🎉🎉🎉 完美成功！7个字段100%提取成功！🎉🎉🎉")
                    print(f"🏆 简化系统达到预期目标！")
                elif success_rate >= 85:
                    print(f"\n🎊 优秀成绩！系统表现出色！")
                else:
                    print(f"\n👍 良好成绩！系统运行正常！")
                
                print(f"\n🚀 简化系统特点:")
                print("=" * 50)
                print(f"✅ 保留核心功能")
                print(f"✅ 移除复杂模块")
                print(f"✅ 简单易用")
                print(f"✅ 高效稳定")
                
            else:
                print(f"❌ 测试失败")
                print(f"⏱️  耗时: {duration:.2f} 秒")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 测试异常: {str(e)}")
            print(f"⏱️  耗时: {duration:.2f} 秒")

if __name__ == "__main__":
    asyncio.run(final_test())
