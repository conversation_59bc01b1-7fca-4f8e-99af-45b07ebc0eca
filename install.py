"""
一键安装脚本
自动安装所有依赖和配置环境
"""

import subprocess
import sys
import os
from typing import List

def run_command(command: str, description: str = "") -> bool:
    """运行命令"""
    if description:
        print(f"🔧 {description}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ 成功: {description or command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {description or command}")
        print(f"错误: {e.stderr}")
        return False

def check_python_version() -> bool:
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    if sys.version_info < (3, 7):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies() -> bool:
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        print("⚠️  pip升级失败，继续安装依赖")
    
    # 安装requirements.txt中的依赖
    if os.path.exists("requirements.txt"):
        success = run_command(
            f"{sys.executable} -m pip install -r requirements.txt",
            "安装requirements.txt中的依赖"
        )
        if not success:
            print("❌ 依赖安装失败")
            return False
    else:
        # 手动安装关键依赖
        dependencies = [
            "crawl4ai>=0.6.0",
            "beautifulsoup4>=4.12.0",
            "pandas>=2.0.0",
            "openpyxl>=3.1.0",
            "lxml>=4.9.0",
            "aiohttp>=3.8.0",
            "curl_cffi>=0.5.0",
            "DrissionPage>=4.0.0",
            "fake_useragent>=1.4.0"
        ]
        
        for dep in dependencies:
            if not run_command(f"{sys.executable} -m pip install {dep}", f"安装 {dep}"):
                print(f"⚠️  {dep} 安装失败，可能影响功能")
    
    return True

def setup_directories() -> bool:
    """设置目录结构"""
    print("\n📁 设置目录结构...")
    
    directories = ["output", "logs"]
    
    for dir_name in directories:
        try:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ 创建目录: {dir_name}")
            else:
                print(f"📁 目录已存在: {dir_name}")
        except Exception as e:
            print(f"❌ 创建目录失败 {dir_name}: {str(e)}")
            return False
    
    return True

def create_config_files() -> bool:
    """创建配置文件"""
    print("\n⚙️  创建配置文件...")
    
    try:
        # 创建示例配置文件
        if not os.path.exists("crawler_config.json"):
            from config_manager import config_manager
            config_manager.create_sample_config()
            print("✅ 创建配置文件: crawler_config.json")
        else:
            print("📋 配置文件已存在: crawler_config.json")
        
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {str(e)}")
        return False

def install_playwright() -> bool:
    """安装Playwright浏览器"""
    print("\n🎭 安装Playwright浏览器...")
    
    try:
        # 安装playwright
        if not run_command(f"{sys.executable} -m pip install playwright", "安装Playwright"):
            return False
        
        # 安装浏览器
        if not run_command("playwright install chromium", "安装Chromium浏览器"):
            print("⚠️  Chromium安装失败，可能影响某些功能")
        
        return True
    except Exception as e:
        print(f"❌ Playwright安装失败: {str(e)}")
        return False

def verify_installation() -> bool:
    """验证安装"""
    print("\n🔍 验证安装...")
    
    # 检查关键模块
    critical_modules = [
        "crawl4ai", "beautifulsoup4", "pandas", "aiohttp"
    ]
    
    failed_modules = []
    
    for module in critical_modules:
        try:
            __import__(module.replace("-", "_"))
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 不可用")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"\n❌ 以下模块安装失败: {', '.join(failed_modules)}")
        return False
    
    print("\n✅ 所有关键模块验证通过")
    return True

def main():
    """主安装流程"""
    print("🚀 BOSS直聘爬虫系统 - 一键安装")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        print("\n❌ 安装失败：Python版本不兼容")
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败：依赖安装失败")
        return False
    
    # 设置目录
    if not setup_directories():
        print("\n❌ 安装失败：目录设置失败")
        return False
    
    # 创建配置文件
    if not create_config_files():
        print("\n⚠️  配置文件创建失败，但可以继续")
    
    # 安装Playwright（可选）
    playwright_choice = input("\n是否安装Playwright浏览器？(推荐) (y/n): ").strip().lower()
    if playwright_choice == 'y':
        install_playwright()
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 安装验证失败")
        return False
    
    print("\n🎉 安装完成！")
    print("=" * 60)
    print("📋 下一步:")
    print("  1. 运行 'python quick_start.py' 进行快速启动")
    print("  2. 或运行 'python main.py' 开始使用")
    print("  3. 查看 README.md 了解详细使用方法")
    
    # 询问是否立即启动
    start_choice = input("\n是否立即运行快速启动？(y/n): ").strip().lower()
    if start_choice == 'y':
        try:
            import quick_start
            quick_start.main()
        except Exception as e:
            print(f"❌ 快速启动失败: {str(e)}")
            print("请手动运行: python quick_start.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 安装已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装异常: {str(e)}")
        sys.exit(1)
