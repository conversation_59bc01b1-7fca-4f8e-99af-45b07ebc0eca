# BOSS直聘爬虫

高效的BOSS直聘职位信息爬虫，成功绕过安全检查，提取完整职位数据。

## 功能特点

✅ **成功绕过安全检查** - 使用GitHub最新反反爬技术  
✅ **高准确率数据提取** - 提取7个核心字段  
✅ **多格式输出** - 同时生成JSON和Excel文件  
✅ **智能重试机制** - 多种访问策略自动切换  

## 提取字段

1. 岗位名称
2. 薪资情况  
3. 待遇情况
4. 职位描述
5. 公司简介
6. 工作地点
7. 实际网址

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 基本使用
```python
import asyncio
from boss_crawler import BOSSCrawler

async def main():
    async with BOSSCrawler() as crawler:
        # 爬取单个职位
        job_data = await crawler.crawl_job_detail("https://www.zhipin.com/job_detail/xxx.html")
        print(job_data)

asyncio.run(main())
```

### 批量爬取
```bash
# 使用批量爬取脚本
python batch_crawler.py

# 或者创建urls.txt文件，每行一个URL，然后运行
python batch_crawler.py
```

### 性能测试
```bash
# 测试爬虫性能和准确性
python performance_test.py
```

## 核心技术

- **BOSS安全检查绕过器** - 专门针对BOSS直聘安全验证
- **DrissionPage** - 自研内核，无webdriver检测痕迹  
- **curl_cffi** - 完美模拟浏览器TLS/JA3指纹
- **多层反检测** - 完全隐藏自动化痕迹

## 文件说明

### 核心文件
- `main.py` - 简单易用的主程序入口
- `batch_crawler.py` - 批量爬取脚本
- `performance_test.py` - 性能测试脚本
- `boss_crawler.py` - 主爬虫引擎
- `boss_security_bypass.py` - BOSS安全检查绕过器

### 数据处理
- `data_extractor.py` - 数据提取器
- `data_processor.py` - 数据处理器
- `config.py` - 配置文件

### 示例文件
- `urls_example.txt` - URL示例文件

## 注意事项

- 请遵守网站robots.txt协议
- 建议设置合理的请求间隔
- 仅用于学习和研究目的
