# BOSS直聘爬虫

🚀 **高效的BOSS直聘职位信息爬虫**，成功绕过安全检查，7个字段100%提取成功！

## ✅ 核心功能

- **7个字段完整提取** - 岗位名称、薪资、待遇、职位描述、公司简介、工作地点、网址
- **绕过安全检查** - 使用最新反反爬技术
- **智能搜索收集** - 自动收集职位URL
- **批量处理** - 支持大量URL批量爬取
- **多格式输出** - JSON和Excel格式

## 🚀 快速使用

### 安装依赖
```bash
pip install -r requirements.txt
```

### 基本使用
```bash
# 交互式模式
python main.py

# 从文件批量爬取
python main.py urls.txt
```

### 功能选项
运行 `python main.py` 后选择：
1. 爬取单个职位URL
2. 批量爬取多个URL
3. 从文件批量爬取
4. 智能搜索收集URL
5. 完整工作流（搜索+爬取+分析）
6. 查看性能统计
7. 查看配置

## 🔧 核心技术

- **BOSS安全检查绕过器** - 专门针对BOSS直聘安全验证
- **终极数据提取器** - 基于深度页面分析的精确选择器
- **智能重试机制** - 多种访问策略自动切换
- **性能监控** - 实时统计和自动优化

## 📁 主要文件

- `main.py` - 主程序入口
- `boss_crawler.py` - 核心爬虫引擎
- `boss_security_bypass.py` - 安全检查绕过器
- `ultimate_data_extractor.py` - 数据提取器
- `requirements.txt` - 依赖列表

## ⚠️ 注意事项

- 仅用于学习和研究目的
- 请遵守网站使用协议
- 建议设置合理的请求间隔
