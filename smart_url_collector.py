"""
智能URL收集器
自动从BOSS直聘搜索页面收集职位URL
"""

import asyncio
import re
import random
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
from boss_security_bypass import boss_security_bypass
from logger_manager import logger

class SmartURLCollector:
    """智能URL收集器"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.search_url = "https://www.zhipin.com/web/geek/job"
        
    async def collect_job_urls(self, keyword: str = "", city: str = "", 
                             pages: int = 5, position: str = "") -> List[str]:
        """收集职位URL"""
        logger.info(f"开始收集职位URL: 关键词={keyword}, 城市={city}, 页数={pages}")
        
        all_urls = []
        
        for page in range(1, pages + 1):
            logger.progress(f"收集第 {page}/{pages} 页")
            
            try:
                # 构建搜索URL
                search_url = self._build_search_url(keyword, city, page, position)
                logger.debug(f"搜索URL: {search_url}")
                
                # 获取搜索页面内容
                html_content = await self._get_search_page(search_url)
                
                if html_content:
                    # 提取职位URL
                    page_urls = self._extract_job_urls(html_content)
                    
                    if page_urls:
                        all_urls.extend(page_urls)
                        logger.success(f"第 {page} 页收集到 {len(page_urls)} 个URL")
                    else:
                        logger.warning(f"第 {page} 页未找到职位URL")
                        
                        # 如果连续几页都没有URL，可能是搜索结束
                        if page > 2:
                            logger.info("可能已到达搜索结果末尾")
                            break
                else:
                    logger.error(f"第 {page} 页获取失败")
                
                # 页面间延时
                await asyncio.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logger.error(f"第 {page} 页收集异常: {str(e)}")
                continue
        
        # 去重
        unique_urls = list(dict.fromkeys(all_urls))
        logger.success(f"总共收集到 {len(unique_urls)} 个唯一职位URL")
        
        return unique_urls
    
    def _build_search_url(self, keyword: str, city: str, page: int, position: str) -> str:
        """构建搜索URL"""
        params = []
        
        if keyword:
            params.append(f"query={keyword}")
        
        if city:
            # 城市代码映射（简化版）
            city_codes = {
                "北京": "101010100",
                "上海": "101020100", 
                "广州": "101280100",
                "深圳": "101280600",
                "杭州": "101210100",
                "南京": "101190100",
                "苏州": "101190400",
                "成都": "101270100",
                "武汉": "101200100",
                "西安": "101110100"
            }
            city_code = city_codes.get(city, "")
            if city_code:
                params.append(f"city={city_code}")
        
        if position:
            params.append(f"position={position}")
        
        if page > 1:
            params.append(f"page={page}")
        
        # 添加一些默认参数
        params.extend([
            "ka=sel-city",
            "jobType=",
            "salary=",
            "experience=",
            "degree=",
            "scale=",
            "stage="
        ])
        
        query_string = "&".join(params)
        return f"{self.search_url}?{query_string}"
    
    async def _get_search_page(self, search_url: str) -> Optional[str]:
        """获取搜索页面内容"""
        try:
            # 使用BOSS安全检查绕过器
            html_content = await boss_security_bypass.method_wait_and_extract_token(search_url)
            
            if html_content and len(html_content) > 10000:
                return html_content
            
            # 备用方法
            html_content = await boss_security_bypass.method_direct_bypass(search_url)
            
            if html_content and len(html_content) > 10000:
                return html_content
            
            return None
            
        except Exception as e:
            logger.error(f"获取搜索页面失败: {str(e)}")
            return None
    
    def _extract_job_urls(self, html_content: str) -> List[str]:
        """从搜索页面提取职位URL"""
        urls = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 多种选择器尝试
            selectors = [
                "a[href*='/job_detail/']",
                ".job-card-wrapper a",
                ".job-card a",
                ".job-list-item a",
                "a[ka='search_list_']"
            ]
            
            for selector in selectors:
                links = soup.select(selector)
                
                for link in links:
                    href = link.get('href', '')
                    
                    if '/job_detail/' in href:
                        # 构建完整URL
                        if href.startswith('/'):
                            full_url = urljoin(self.base_url, href)
                        else:
                            full_url = href
                        
                        # 验证URL格式
                        if self._is_valid_job_url(full_url):
                            urls.append(full_url)
            
            # 去重
            unique_urls = list(dict.fromkeys(urls))
            
            # 从页面文本中提取URL（备用方法）
            if not unique_urls:
                url_pattern = r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html'
                text_urls = re.findall(url_pattern, html_content)
                unique_urls.extend(text_urls)
            
            return list(dict.fromkeys(unique_urls))
            
        except Exception as e:
            logger.error(f"提取职位URL失败: {str(e)}")
            return []
    
    def _is_valid_job_url(self, url: str) -> bool:
        """验证是否是有效的职位URL"""
        try:
            parsed = urlparse(url)
            
            # 检查域名
            if parsed.netloc != 'www.zhipin.com':
                return False
            
            # 检查路径
            if not parsed.path.startswith('/job_detail/'):
                return False
            
            # 检查是否有职位ID
            job_id_pattern = r'/job_detail/([a-zA-Z0-9]+)\.html'
            if not re.search(job_id_pattern, parsed.path):
                return False
            
            return True
            
        except Exception:
            return False
    
    async def collect_by_search_terms(self, search_terms: List[Dict[str, str]], 
                                    max_pages_per_term: int = 3) -> List[str]:
        """根据多个搜索条件收集URL"""
        logger.info(f"开始批量收集，搜索条件数: {len(search_terms)}")
        
        all_urls = []
        
        for i, term in enumerate(search_terms, 1):
            logger.progress(f"处理搜索条件 {i}/{len(search_terms)}: {term}")
            
            try:
                urls = await self.collect_job_urls(
                    keyword=term.get("keyword", ""),
                    city=term.get("city", ""),
                    pages=max_pages_per_term,
                    position=term.get("position", "")
                )
                
                all_urls.extend(urls)
                logger.success(f"搜索条件 {i} 收集到 {len(urls)} 个URL")
                
                # 搜索条件间延时
                await asyncio.sleep(random.uniform(5, 10))
                
            except Exception as e:
                logger.error(f"搜索条件 {i} 处理异常: {str(e)}")
                continue
        
        # 最终去重
        unique_urls = list(dict.fromkeys(all_urls))
        logger.success(f"批量收集完成，总计 {len(unique_urls)} 个唯一URL")
        
        return unique_urls
    
    def save_urls_to_file(self, urls: List[str], filename: str = "collected_urls.txt"):
        """保存URL到文件"""
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write("# BOSS直聘职位URL列表\n")
                f.write(f"# 总计: {len(urls)} 个URL\n")
                f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for url in urls:
                    f.write(url + "\n")
            
            logger.success(f"URL列表已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存URL文件失败: {str(e)}")
            return None

# 全局智能URL收集器实例
smart_url_collector = SmartURLCollector()
