"""
分析安全检查页面
找出具体的检测机制和绕过方法
"""

import asyncio
import re
from boss_crawler import BOSSCrawler

async def analyze_security_page():
    """分析安全检查页面内容"""
    print("🔍 分析BOSS直聘安全检查页面")
    print("=" * 60)
    
    test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
    
    async with BOSSCrawler() as crawler:
        # 使用第一个方法获取页面内容
        method = crawler.access_methods[0]
        print(f"使用方法: {method.__name__}")
        
        html_content = await method(test_url)
        
        if html_content:
            print(f"✅ 获取页面内容，长度: {len(html_content)}")
            
            # 保存完整页面用于分析
            with open("security_page_analysis.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("💾 页面内容已保存到 security_page_analysis.html")
            
            # 分析页面内容
            print("\n🔍 页面内容分析:")
            print("-" * 40)
            
            # 检查标题
            title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
            if title_match:
                title = title_match.group(1).strip()
                print(f"📄 页面标题: {title}")
            
            # 检查关键词
            security_keywords = [
                "安全验证", "security", "验证码", "captcha", "cloudflare", 
                "bot detection", "access denied", "请稍后再试", "请求过于频繁", 
                "blocked", "防护", "检测", "人机验证", "滑动验证", "点击验证"
            ]
            
            found_keywords = []
            for keyword in security_keywords:
                if keyword.lower() in html_content.lower():
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"🚨 发现安全检查关键词: {', '.join(found_keywords)}")
            
            # 检查JavaScript
            js_patterns = [
                r'challenge',
                r'verify',
                r'captcha',
                r'protection',
                r'anti.*bot',
                r'fingerprint',
                r'detection'
            ]
            
            js_found = []
            for pattern in js_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    js_found.extend(matches)
            
            if js_found:
                print(f"🔧 发现相关JavaScript: {', '.join(set(js_found))}")
            
            # 检查meta标签
            meta_matches = re.findall(r'<meta[^>]*>', html_content, re.IGNORECASE)
            print(f"📋 Meta标签数量: {len(meta_matches)}")
            
            # 检查是否有重定向
            redirect_patterns = [
                r'window\.location',
                r'document\.location',
                r'location\.href',
                r'location\.replace'
            ]
            
            redirects = []
            for pattern in redirect_patterns:
                if re.search(pattern, html_content, re.IGNORECASE):
                    redirects.append(pattern)
            
            if redirects:
                print(f"🔄 发现重定向代码: {', '.join(redirects)}")
            
            # 检查页面大小特征
            if len(html_content) < 10000:
                print("⚠️  页面内容较短，可能是安全检查页面")
            elif len(html_content) > 100000:
                print("✅ 页面内容较长，可能包含实际内容")
            
            # 检查是否包含职位相关内容
            job_keywords = ["职位", "薪资", "工作", "公司", "岗位", "招聘", "面试", "简历"]
            job_found = []
            for keyword in job_keywords:
                if keyword in html_content:
                    job_found.append(keyword)
            
            if job_found:
                print(f"📋 发现职位相关内容: {', '.join(job_found)}")
            else:
                print("❌ 未发现职位相关内容")
            
            # 提取前500字符用于快速查看
            print(f"\n📝 页面开头内容预览:")
            print("-" * 40)
            preview = html_content[:500].replace('\n', ' ').replace('\r', ' ')
            preview = ' '.join(preview.split())  # 清理多余空格
            print(preview)
            
            if len(html_content) > 500:
                print("...")
            
        else:
            print("❌ 未能获取页面内容")

if __name__ == "__main__":
    asyncio.run(analyze_security_page())
