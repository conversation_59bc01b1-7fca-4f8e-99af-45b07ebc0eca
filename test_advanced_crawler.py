"""
测试高级反反爬爬虫
验证新的反检测技术是否有效
"""

import asyncio
import time
from boss_crawler import BOSSCrawler

async def test_advanced_crawler():
    """测试高级爬虫功能"""
    print("🚀 开始测试高级反反爬爬虫")
    print("=" * 60)
    
    # 测试URL（真实的BOSS直聘职位）
    test_urls = [
        "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        "https://www.zhipin.com/job_detail/1234567890abcdef1XB93NS5FlRQ.html",  # 可能不存在，用于测试
    ]
    
    start_time = time.time()
    
    async with BOSSCrawler() as crawler:
        print(f"📋 准备测试 {len(test_urls)} 个URL")
        print(f"🔧 可用的访问方法数量: {len(crawler.access_methods)}")
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n{'='*50}")
            print(f"🎯 测试 {i}/{len(test_urls)}: {url}")
            print(f"{'='*50}")
            
            job_data = await crawler.crawl_job_detail(url)
            
            if job_data:
                print(f"✅ 成功获取数据:")
                for key, value in job_data.items():
                    if value:
                        print(f"  {key}: {value[:100]}{'...' if len(str(value)) > 100 else ''}")
                    else:
                        print(f"  {key}: (空)")
            else:
                print(f"❌ 未能获取数据")
            
            print(f"\n⏱️  当前耗时: {time.time() - start_time:.2f} 秒")
    
    total_time = time.time() - start_time
    print(f"\n🏁 测试完成！总耗时: {total_time:.2f} 秒")

if __name__ == "__main__":
    asyncio.run(test_advanced_crawler())
