{"crawler": {"max_retries": 3, "timeout": 30, "delay_between_requests": 2, "max_concurrent": 1, "user_agent_rotation": true}, "security_bypass": {"max_wait_time": 45, "check_interval": 1, "enable_smart_detection": true, "fallback_methods": true}, "data_extraction": {"min_description_length": 50, "min_company_intro_length": 20, "enable_text_fallback": true, "strict_validation": false}, "output": {"save_json": true, "save_excel": true, "output_directory": "output", "filename_timestamp": true, "backup_failed_urls": true}, "logging": {"level": "INFO", "save_to_file": true, "log_directory": "logs"}}