"""
配置管理器
支持动态配置和环境变量
"""

import os
import json
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "crawler_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        # 默认配置
        default_config = {
            "crawler": {
                "max_retries": 3,
                "timeout": 30,
                "delay_between_requests": 2,
                "max_concurrent": 1,
                "user_agent_rotation": True
            },
            "security_bypass": {
                "max_wait_time": 45,
                "check_interval": 1,
                "enable_smart_detection": True,
                "fallback_methods": True
            },
            "data_extraction": {
                "min_description_length": 50,
                "min_company_intro_length": 20,
                "enable_text_fallback": True,
                "strict_validation": False
            },
            "output": {
                "save_json": True,
                "save_excel": True,
                "output_directory": "output",
                "filename_timestamp": True,
                "backup_failed_urls": True
            },
            "logging": {
                "level": "INFO",
                "save_to_file": True,
                "log_directory": "logs"
            }
        }
        
        # 尝试从文件加载配置
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    file_config = json.load(f)
                    # 合并配置
                    self._merge_config(default_config, file_config)
            except Exception as e:
                print(f"⚠️  配置文件加载失败，使用默认配置: {str(e)}")
        
        # 从环境变量覆盖配置
        self._load_from_env(default_config)
        
        return default_config
    
    def _merge_config(self, default: Dict[str, Any], override: Dict[str, Any]):
        """合并配置"""
        for key, value in override.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def _load_from_env(self, config: Dict[str, Any]):
        """从环境变量加载配置"""
        env_mappings = {
            "CRAWLER_MAX_RETRIES": ("crawler", "max_retries", int),
            "CRAWLER_TIMEOUT": ("crawler", "timeout", int),
            "CRAWLER_DELAY": ("crawler", "delay_between_requests", float),
            "SECURITY_MAX_WAIT": ("security_bypass", "max_wait_time", int),
            "OUTPUT_DIR": ("output", "output_directory", str),
            "LOG_LEVEL": ("logging", "level", str),
        }
        
        for env_var, (section, key, type_func) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    config[section][key] = type_func(value)
                except ValueError:
                    print(f"⚠️  环境变量 {env_var} 值无效: {value}")
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(section, {}).get(key, default)
    
    def set(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"❌ 配置保存失败: {str(e)}")
    
    def create_sample_config(self):
        """创建示例配置文件"""
        sample_config = {
            "crawler": {
                "max_retries": 3,
                "timeout": 30,
                "delay_between_requests": 2,
                "max_concurrent": 1,
                "user_agent_rotation": True
            },
            "security_bypass": {
                "max_wait_time": 45,
                "check_interval": 1,
                "enable_smart_detection": True,
                "fallback_methods": True
            },
            "data_extraction": {
                "min_description_length": 50,
                "min_company_intro_length": 20,
                "enable_text_fallback": True,
                "strict_validation": False
            },
            "output": {
                "save_json": True,
                "save_excel": True,
                "output_directory": "output",
                "filename_timestamp": True,
                "backup_failed_urls": True
            },
            "logging": {
                "level": "INFO",
                "save_to_file": True,
                "log_directory": "logs"
            }
        }
        
        sample_file = "crawler_config_sample.json"
        try:
            with open(sample_file, "w", encoding="utf-8") as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            print(f"✅ 示例配置文件已创建: {sample_file}")
        except Exception as e:
            print(f"❌ 示例配置文件创建失败: {str(e)}")
    
    def print_config(self):
        """打印当前配置"""
        print("📋 当前配置:")
        print("=" * 50)
        for section, settings in self.config.items():
            print(f"[{section}]")
            for key, value in settings.items():
                print(f"  {key}: {value}")
            print()

# 全局配置管理器实例
config_manager = ConfigManager()
