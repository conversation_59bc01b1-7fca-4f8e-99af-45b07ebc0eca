"""
日志管理器
统一的日志记录和管理
"""

import os
import logging
from datetime import datetime
from typing import Optional
from config_manager import config_manager

class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 获取配置
        log_level = config_manager.get("logging", "level", "INFO")
        save_to_file = config_manager.get("logging", "save_to_file", True)
        log_directory = config_manager.get("logging", "log_directory", "logs")
        
        # 创建日志器
        self.logger = logging.getLogger("boss_crawler")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if save_to_file:
            try:
                # 创建日志目录
                os.makedirs(log_directory, exist_ok=True)
                
                # 创建日志文件
                timestamp = datetime.now().strftime("%Y%m%d")
                log_file = os.path.join(log_directory, f"crawler_{timestamp}.log")
                
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
                
            except Exception as e:
                print(f"⚠️  日志文件创建失败: {str(e)}")
    
    def info(self, message: str):
        """记录信息日志"""
        if self.logger:
            self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        if self.logger:
            self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        if self.logger:
            self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        if self.logger:
            self.logger.debug(message)
    
    def success(self, message: str):
        """记录成功日志"""
        self.info(f"✅ {message}")
    
    def failure(self, message: str):
        """记录失败日志"""
        self.error(f"❌ {message}")
    
    def progress(self, message: str):
        """记录进度日志"""
        self.info(f"🎯 {message}")
    
    def log_crawl_start(self, url: str):
        """记录爬取开始"""
        self.info(f"开始爬取: {url}")
    
    def log_crawl_success(self, url: str, duration: float, fields_count: int):
        """记录爬取成功"""
        self.success(f"爬取成功: {url} | 耗时: {duration:.2f}s | 字段: {fields_count}/7")
    
    def log_crawl_failure(self, url: str, error: str):
        """记录爬取失败"""
        self.failure(f"爬取失败: {url} | 错误: {error}")
    
    def log_security_bypass(self, method: str, success: bool):
        """记录安全绕过"""
        if success:
            self.success(f"安全绕过成功: {method}")
        else:
            self.warning(f"安全绕过失败: {method}")
    
    def log_batch_start(self, total_count: int):
        """记录批量爬取开始"""
        self.info(f"开始批量爬取: {total_count} 个URL")
    
    def log_batch_complete(self, success_count: int, total_count: int, duration: float):
        """记录批量爬取完成"""
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        self.success(f"批量爬取完成: {success_count}/{total_count} ({success_rate:.1f}%) | 总耗时: {duration:.1f}s")
    
    def log_data_save(self, file_path: str, count: int):
        """记录数据保存"""
        self.success(f"数据已保存: {file_path} | 记录数: {count}")

# 全局日志管理器实例
logger = LoggerManager()
