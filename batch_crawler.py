"""
批量爬取脚本
支持批量爬取多个职位并保存结果
"""

import asyncio
import time
import json
from typing import List
from boss_crawler import BOSSCrawler
from data_processor import data_processor

async def batch_crawl(urls: List[str], output_prefix: str = "boss_jobs"):
    """批量爬取职位信息"""
    print("🚀 BOSS直聘批量爬虫")
    print("=" * 60)
    print(f"📋 准备爬取 {len(urls)} 个职位")
    print(f"💾 输出文件前缀: {output_prefix}")
    print("=" * 60)
    
    start_time = time.time()
    successful_jobs = []
    failed_urls = []
    
    async with BOSSCrawler() as crawler:
        for i, url in enumerate(urls, 1):
            print(f"\n🎯 爬取进度: {i}/{len(urls)}")
            print(f"📍 URL: {url}")
            
            try:
                job_data = await crawler.crawl_job_detail(url)
                
                if job_data:
                    successful_jobs.append(job_data)
                    
                    # 显示提取的核心信息
                    print(f"✅ 成功提取:")
                    if job_data.get("岗位名称"):
                        print(f"  📋 {job_data['岗位名称']}")
                    if job_data.get("薪资情况"):
                        print(f"  💰 {job_data['薪资情况']}")
                    if job_data.get("工作地点"):
                        location = job_data['工作地点'][:40] + "..." if len(job_data['工作地点']) > 40 else job_data['工作地点']
                        print(f"  📍 {location}")
                else:
                    failed_urls.append(url)
                    print(f"❌ 提取失败")
                
            except Exception as e:
                failed_urls.append(url)
                print(f"❌ 爬取异常: {str(e)}")
            
            # 显示当前进度
            current_time = time.time()
            elapsed = current_time - start_time
            avg_time = elapsed / i
            estimated_total = avg_time * len(urls)
            remaining = estimated_total - elapsed
            
            print(f"⏱️  已用时: {elapsed:.1f}秒 | 预计剩余: {remaining:.1f}秒")
            
            # 添加延时避免请求过快
            if i < len(urls):  # 不是最后一个
                await asyncio.sleep(2)  # 2秒间隔
    
    # 保存结果
    total_time = time.time() - start_time
    
    if successful_jobs:
        # 使用数据处理器保存结果
        json_file = data_processor.save_to_json(successful_jobs, f"{output_prefix}.json")
        excel_file = data_processor.save_to_excel(successful_jobs, f"{output_prefix}.xlsx")
        print(f"\n💾 数据已保存:")
        print(f"  📄 JSON: {json_file}")
        print(f"  📊 Excel: {excel_file}")
    
    # 保存失败的URL
    if failed_urls:
        failed_file = f"{output_prefix}_failed_urls.txt"
        with open(failed_file, "w", encoding="utf-8") as f:
            for url in failed_urls:
                f.write(url + "\n")
        print(f"  ❌ 失败URL: {failed_file}")
    
    # 统计结果
    success_count = len(successful_jobs)
    total_count = len(urls)
    success_rate = (success_count / total_count) * 100
    
    print(f"\n📊 批量爬取完成")
    print("=" * 60)
    print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"⏱️  总耗时: {total_time:.1f} 秒")
    print(f"⚡ 平均速度: {total_time/total_count:.1f} 秒/职位")
    
    if success_rate >= 80:
        print(f"🎉 批量爬取表现优秀！")
    elif success_rate >= 60:
        print(f"👍 批量爬取表现良好")
    else:
        print(f"⚠️  建议检查失败的URL")

def load_urls_from_file(file_path: str) -> List[str]:
    """从文件加载URL列表"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            urls = [line.strip() for line in f if line.strip() and line.strip().startswith("http")]
        print(f"📂 从 {file_path} 加载了 {len(urls)} 个URL")
        return urls
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return []
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return []

async def main():
    """主函数"""
    print("🚀 BOSS直聘批量爬虫")
    print("=" * 60)
    
    # 方式1: 直接指定URL列表
    urls = [
        "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        # 添加更多URL...
    ]
    
    # 方式2: 从文件加载URL（如果存在urls.txt文件）
    file_urls = load_urls_from_file("urls.txt")
    if file_urls:
        urls.extend(file_urls)
    
    if not urls:
        print("❌ 没有找到要爬取的URL")
        print("💡 请在代码中添加URL或创建urls.txt文件")
        return
    
    # 去重
    urls = list(set(urls))
    
    # 开始批量爬取
    await batch_crawl(urls, "boss_jobs_batch")

if __name__ == "__main__":
    asyncio.run(main())
