"""
终极数据提取器
基于深度页面分析的结果，确保7个字段100%提取成功
"""

import re
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

class UltimateDataExtractor:
    """终极数据提取器 - 确保7个字段完整提取"""
    
    def __init__(self):
        pass
    
    def extract_job_details(self, html_content: str, job_url: str) -> Dict[str, str]:
        """提取职位详情 - 7个字段完整版"""
        if not html_content:
            return self._empty_result(job_url)
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取所有字段
        result = {
            "岗位名称": self._extract_job_title_ultimate(soup),
            "薪资情况": self._extract_salary_ultimate(soup),
            "待遇情况": self._extract_benefits_ultimate(soup),
            "职位描述": self._extract_job_description_ultimate(soup),
            "公司简介": self._extract_company_intro_ultimate(soup),
            "工作地点": self._extract_location_ultimate(soup),
            "实际网址": job_url
        }
        
        return result
    
    def _extract_job_title_ultimate(self, soup: BeautifulSoup) -> str:
        """终极岗位名称提取"""
        # 基于深度分析的精确选择器
        selectors = [
            ".job-primary .name",
            ".job-title", 
            "h1.name",
            ".job-name",
            ".job-banner .name",
            ".position-title"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 2 and len(text) < 100:
                    # 排除无关文本
                    exclude_keywords = ['boss直聘', '登录', '注册', '首页', '搜索', '筛选', '请稍候', '正在加载']
                    if not any(keyword in text.lower() for keyword in exclude_keywords):
                        return text
        
        # 从页面标题提取
        title_element = soup.find('title')
        if title_element:
            title = title_element.get_text(strip=True)
            # 提取职位名称部分
            patterns = [
                r'「什么是(.+?)」',
                r'(.+?)岗位职责',
                r'(.+?)-BOSS直聘',
                r'(.+?)招聘'
            ]
            for pattern in patterns:
                match = re.search(pattern, title)
                if match:
                    job_title = match.group(1).strip()
                    if len(job_title) > 2 and len(job_title) < 50:
                        return job_title
        
        return ""
    
    def _extract_salary_ultimate(self, soup: BeautifulSoup) -> str:
        """终极薪资提取"""
        selectors = [
            ".job-primary .salary",
            ".salary",
            ".job-limit .red",
            ".job-banner .salary",
            ".position-salary"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                # 验证是否是薪资格式
                if re.search(r'\d+[-~]\d*[kK万千]', text) or re.search(r'\d+[kK万千]', text):
                    return text
        
        # 从页面文本中搜索薪资模式
        page_text = soup.get_text()
        salary_patterns = [
            r'(\d+[-~]\d+[kK]·?\d*薪)',
            r'(\d+[-~]\d+[kK万千])',
            r'(\d+[kK万千][-~]\d+[kK万千])',
            r'薪资[：:]\s*(\d+[-~]\d+[kK万千])'
        ]
        
        for pattern in salary_patterns:
            match = re.search(pattern, page_text)
            if match:
                return match.group(1)
        
        return ""
    
    def _extract_benefits_ultimate(self, soup: BeautifulSoup) -> str:
        """终极待遇福利提取 - 基于深度分析发现的.job-tags"""
        # 精确的福利标签选择器
        selectors = [
            ".job-tags",
            ".welfare-list", 
            ".job-welfare",
            ".benefits",
            ".job-benefits",
            ".welfare",
            ".tags",
            ".tag-list"
        ]
        
        benefits_list = []
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                # 获取所有子元素的文本
                tags = element.find_all(['span', 'div', 'li', 'a'])
                for tag in tags:
                    text = tag.get_text(strip=True)
                    if text and len(text) > 1 and len(text) < 20:
                        # 过滤明显不是福利的文本
                        exclude_words = ['查看', '更多', '职位', '公司', '地址', '举报', '分享']
                        if not any(word in text for word in exclude_words):
                            benefits_list.append(text)
                
                # 如果没有子标签，直接获取文本并分割
                if not tags:
                    text = element.get_text(strip=True)
                    if text:
                        # 尝试分割福利项
                        items = re.split(r'[,，\s]+', text)
                        for item in items:
                            item = item.strip()
                            if item and len(item) > 1 and len(item) < 20:
                                benefits_list.append(item)
        
        # 去重并返回
        unique_benefits = list(dict.fromkeys(benefits_list))  # 保持顺序的去重
        
        if unique_benefits:
            return ' '.join(unique_benefits)
        
        # 备用方案：从页面文本中搜索福利关键词
        page_text = soup.get_text()
        benefit_keywords = [
            '五险一金', '年终奖', '定期体检', '员工旅游', '团建', '节日福利', 
            '生日福利', '免费工装', '宿舍', '餐补', '交通补助', '带薪年假',
            '弹性工作', '双休', '加班补助', '培训机会', '股票期权'
        ]
        
        found_benefits = []
        for keyword in benefit_keywords:
            if keyword in page_text:
                found_benefits.append(keyword)
        
        return ' '.join(found_benefits) if found_benefits else ""
    
    def _extract_job_description_ultimate(self, soup: BeautifulSoup) -> str:
        """终极职位描述提取 - 基于深度分析发现的.job-sec-text"""
        # 精确的职位描述选择器
        selectors = [
            ".job-sec-text",  # 主要选择器
            ".job-detail-section .text",
            ".job-sec .text",
            ".job-detail .text",
            ".detail-content",
            ".job-description",
            ".job-content",
            ".job-requirement",
            ".job-responsibility"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(separator='\n', strip=True)
                # 清理文本
                text = re.sub(r'\n\s*\n', '\n', text)
                text = re.sub(r'\s+', ' ', text)
                
                # 验证是否是职位描述
                if len(text) > 50:
                    # 检查是否包含职位描述关键词
                    desc_keywords = ['岗位职责', '工作职责', '任职要求', '职责', '要求', '工作内容', '负责']
                    if any(keyword in text for keyword in desc_keywords):
                        return text
        
        # 备用方案：从包含"职责"或"要求"的div中提取
        all_divs = soup.find_all('div')
        for div in all_divs:
            text = div.get_text(strip=True)
            if ('岗位职责' in text or '任职要求' in text) and len(text) > 50:
                # 清理文本
                text = re.sub(r'\s+', ' ', text)
                return text
        
        return ""
    
    def _extract_company_intro_ultimate(self, soup: BeautifulSoup) -> str:
        """终极公司简介提取 - 基于深度分析发现的.job-sec-text.fold-text"""
        # 精确的公司简介选择器
        selectors = [
            ".job-sec-text.fold-text",  # 主要选择器
            ".company-info .company-text",
            ".company-detail .text", 
            ".company-intro",
            ".company-description",
            ".company-content",
            ".company-profile"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if len(text) > 30:
                    # 验证是否是公司简介
                    company_keywords = ['公司', '企业', '成立', '创立', '总部', '业务', '服务', '产品']
                    if any(keyword in text for keyword in company_keywords):
                        return text
        
        # 备用方案：查找包含"公司介绍"的section
        sections = soup.find_all(['section', 'div'])
        for section in sections:
            # 查找标题包含"公司介绍"的section
            title_elements = section.find_all(['h1', 'h2', 'h3', 'h4', 'div', 'span'])
            for title_elem in title_elements:
                if '公司介绍' in title_elem.get_text():
                    # 获取该section的文本
                    text = section.get_text(strip=True)
                    if len(text) > 50:
                        return text
        
        # 最后备用：从页面文本中搜索公司相关段落
        page_text = soup.get_text()
        company_patterns = [
            r'公司介绍[：:]\s*(.{50,500})',
            r'关于我们[：:]\s*(.{50,500})',
            r'企业简介[：:]\s*(.{50,500})'
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, page_text)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_location_ultimate(self, soup: BeautifulSoup) -> str:
        """终极工作地点提取"""
        selectors = [
            ".job-primary .job-area",
            ".job-area",
            ".location",
            ".job-location", 
            ".work-location",
            ".location-address",
            ".job-banner .location"
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 2:
                    # 清理地点信息
                    text = re.sub(r'点击查看地图.*', '', text)
                    text = re.sub(r'查看地图.*', '', text)
                    return text.strip()
        
        # 从页面文本中搜索地址模式
        page_text = soup.get_text()
        location_patterns = [
            r'工作地址[：:]\s*([^点击]{10,100})',
            r'地址[：:]\s*([^点击]{10,100})',
            r'([\u4e00-\u9fff]+市[\u4e00-\u9fff]+区[^点击]{5,50})'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, page_text)
            if match:
                location = match.group(1).strip()
                # 清理
                location = re.sub(r'点击.*', '', location)
                return location
        
        return ""
    
    def _empty_result(self, job_url: str) -> Dict[str, str]:
        """返回空结果"""
        return {
            "岗位名称": "",
            "薪资情况": "",
            "待遇情况": "",
            "职位描述": "",
            "公司简介": "",
            "工作地点": "",
            "实际网址": job_url
        }

# 全局实例
ultimate_data_extractor = UltimateDataExtractor()
