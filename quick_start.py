"""
快速启动脚本
一键启动和配置BOSS直聘爬虫系统
"""

import asyncio
import os
import sys
from system_health_checker import system_health_checker
from config_manager import config_manager
from logger_manager import logger

async def quick_start():
    """快速启动"""
    print("🚀 BOSS直聘爬虫系统 - 快速启动")
    print("=" * 60)
    
    # 第一步：系统健康检查
    print("🔍 第一步：系统健康检查")
    print("-" * 30)
    
    health_report = await system_health_checker.run_full_health_check()
    system_health_checker.print_health_report(health_report)
    
    overall_status = health_report["overall_status"]
    
    if overall_status == "critical":
        print("\n❌ 系统存在严重问题，无法启动")
        print("请根据上述建议修复问题后重试")
        return False
    elif overall_status == "warning":
        print("\n⚠️  系统存在一些问题，但可以继续")
        continue_choice = input("是否继续启动？(y/n): ").strip().lower()
        if continue_choice != 'y':
            return False
    else:
        print("\n✅ 系统健康状态良好")
    
    # 第二步：环境配置
    print("\n🔧 第二步：环境配置")
    print("-" * 30)
    
    # 创建必要目录
    directories = ["output", "logs"]
    for dir_name in directories:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            print(f"✅ 创建目录: {dir_name}")
        else:
            print(f"📁 目录已存在: {dir_name}")
    
    # 检查配置文件
    if not os.path.exists("crawler_config.json"):
        print("📝 创建默认配置文件...")
        config_manager.create_sample_config()
    else:
        print("📋 配置文件已存在")
    
    # 第三步：功能演示
    print("\n🎯 第三步：功能演示")
    print("-" * 30)
    
    demo_choice = input("是否运行功能演示？(y/n): ").strip().lower()
    if demo_choice == 'y':
        await run_demo()
    
    # 第四步：启动主程序
    print("\n🚀 第四步：启动主程序")
    print("-" * 30)
    
    start_choice = input("是否启动交互式主程序？(y/n): ").strip().lower()
    if start_choice == 'y':
        print("正在启动主程序...")
        # 这里可以启动main.py
        print("请运行: python main.py")
    
    print("\n🎉 快速启动完成！")
    print("💡 提示：运行 'python main.py' 开始使用系统")
    
    return True

async def run_demo():
    """运行功能演示"""
    print("🎬 功能演示开始")
    print("-" * 30)
    
    # 演示1：单个URL爬取
    print("📋 演示1：单个URL爬取")
    demo_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
    
    try:
        from boss_crawler import BOSSCrawler
        
        async with BOSSCrawler() as crawler:
            print(f"🎯 测试URL: {demo_url}")
            job_data = await crawler.crawl_job_detail(demo_url)
            
            if job_data:
                extracted_fields = sum(1 for v in job_data.values() if v and v.strip())
                print(f"✅ 演示成功！提取字段: {extracted_fields}/7")
                
                # 显示部分数据
                for field, value in list(job_data.items())[:3]:
                    if value and value.strip():
                        display_value = value[:30] + "..." if len(value) > 30 else value
                        print(f"  {field}: {display_value}")
            else:
                print("❌ 演示失败")
                
    except Exception as e:
        print(f"❌ 演示异常: {str(e)}")
    
    # 演示2：配置查看
    print(f"\n📋 演示2：配置查看")
    print("当前系统配置:")
    config_manager.print_config()
    
    # 演示3：性能统计
    print(f"\n📋 演示3：性能统计")
    try:
        from performance_optimizer import performance_optimizer
        performance_optimizer.print_real_time_stats()
    except Exception as e:
        print(f"性能统计暂无数据: {str(e)}")
    
    print("🎬 功能演示完成")

def check_prerequisites():
    """检查先决条件"""
    print("🔍 检查先决条件...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查关键依赖
    critical_packages = ["aiohttp", "beautifulsoup4"]
    missing_packages = []
    
    for package in critical_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n💡 请先安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 BOSS直聘爬虫系统快速启动器")
    print("=" * 60)
    
    # 检查先决条件
    if not check_prerequisites():
        print("\n❌ 先决条件检查失败，请修复后重试")
        return
    
    print("\n✅ 先决条件检查通过")
    
    # 运行快速启动
    try:
        success = asyncio.run(quick_start())
        if success:
            print("\n🎉 系统已准备就绪！")
        else:
            print("\n⚠️  启动过程中断")
    except KeyboardInterrupt:
        print("\n\n👋 启动已取消")
    except Exception as e:
        print(f"\n❌ 启动异常: {str(e)}")

if __name__ == "__main__":
    main()
