"""
高级批量爬取器
支持并发、重试、统计分析等高级功能
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from boss_crawler import BOSSCrawler
from data_processor import data_processor
from config_manager import config_manager
from logger_manager import logger
from stats_analyzer import stats_analyzer

class AdvancedCrawler:
    """高级批量爬取器"""
    
    def __init__(self):
        self.config = config_manager
        self.logger = logger
        self.stats = stats_analyzer
        
    async def crawl_batch_advanced(self, urls: List[str], output_prefix: str = "boss_jobs_advanced") -> Dict[str, Any]:
        """高级批量爬取"""
        self.logger.log_batch_start(len(urls))
        start_time = time.time()
        
        # 获取配置
        max_retries = self.config.get("crawler", "max_retries", 3)
        delay = self.config.get("crawler", "delay_between_requests", 2)
        
        successful_jobs = []
        failed_urls = []
        retry_urls = []
        
        async with BOSSCrawler() as crawler:
            # 第一轮爬取
            for i, url in enumerate(urls, 1):
                self.logger.progress(f"爬取进度: {i}/{len(urls)} - {url}")
                
                try:
                    job_start_time = time.time()
                    job_data = await crawler.crawl_job_detail(url)
                    job_duration = time.time() - job_start_time
                    
                    if job_data:
                        successful_jobs.append(job_data)
                        fields_count = sum(1 for v in job_data.values() if v and v.strip())
                        self.logger.log_crawl_success(url, job_duration, fields_count)
                    else:
                        retry_urls.append(url)
                        self.logger.warning(f"首次爬取失败，加入重试队列: {url}")
                        
                except Exception as e:
                    retry_urls.append(url)
                    self.logger.log_crawl_failure(url, str(e))
                
                # 延时
                if i < len(urls):
                    await asyncio.sleep(delay)
            
            # 重试失败的URL
            if retry_urls and max_retries > 0:
                self.logger.info(f"开始重试 {len(retry_urls)} 个失败的URL")
                
                for retry_count in range(max_retries):
                    if not retry_urls:
                        break
                    
                    self.logger.info(f"第 {retry_count + 1}/{max_retries} 次重试")
                    current_retry_urls = retry_urls.copy()
                    retry_urls.clear()
                    
                    for url in current_retry_urls:
                        try:
                            job_start_time = time.time()
                            job_data = await crawler.crawl_job_detail(url)
                            job_duration = time.time() - job_start_time
                            
                            if job_data:
                                successful_jobs.append(job_data)
                                fields_count = sum(1 for v in job_data.values() if v and v.strip())
                                self.logger.log_crawl_success(f"{url} (重试)", job_duration, fields_count)
                            else:
                                retry_urls.append(url)
                                
                        except Exception as e:
                            retry_urls.append(url)
                            self.logger.log_crawl_failure(f"{url} (重试)", str(e))
                        
                        await asyncio.sleep(delay)
                
                # 最终失败的URL
                failed_urls = retry_urls
        
        # 计算总耗时
        total_duration = time.time() - start_time
        success_count = len(successful_jobs)
        total_count = len(urls)
        
        self.logger.log_batch_complete(success_count, total_count, total_duration)
        
        # 保存数据
        result = {
            "success_count": success_count,
            "total_count": total_count,
            "failed_count": len(failed_urls),
            "success_rate": (success_count / total_count) * 100 if total_count > 0 else 0,
            "total_duration": total_duration,
            "avg_duration": total_duration / total_count if total_count > 0 else 0,
            "successful_jobs": successful_jobs,
            "failed_urls": failed_urls
        }
        
        # 保存结果文件
        if successful_jobs:
            json_file = data_processor.save_to_json(successful_jobs, f"{output_prefix}.json")
            excel_file = data_processor.save_to_excel(successful_jobs, f"{output_prefix}.xlsx")
            
            self.logger.log_data_save(json_file, len(successful_jobs))
            self.logger.log_data_save(excel_file, len(successful_jobs))
            
            result["json_file"] = json_file
            result["excel_file"] = excel_file
        
        # 保存失败URL
        if failed_urls:
            failed_file = f"output/{output_prefix}_failed_urls.txt"
            try:
                with open(failed_file, "w", encoding="utf-8") as f:
                    for url in failed_urls:
                        f.write(url + "\n")
                self.logger.info(f"失败URL已保存: {failed_file}")
                result["failed_file"] = failed_file
            except Exception as e:
                self.logger.error(f"保存失败URL文件失败: {str(e)}")
        
        # 生成统计分析
        if successful_jobs:
            try:
                report_file = f"output/{output_prefix}_analysis_report.txt"
                report = self.stats.generate_report(successful_jobs, report_file)
                result["analysis_report"] = report
                result["report_file"] = report_file
                
                # 打印简要统计
                self._print_summary(result)
                
            except Exception as e:
                self.logger.error(f"生成分析报告失败: {str(e)}")
        
        return result
    
    def _print_summary(self, result: Dict[str, Any]):
        """打印摘要统计"""
        print(f"\n📊 爬取摘要")
        print("=" * 50)
        print(f"✅ 成功率: {result['success_count']}/{result['total_count']} ({result['success_rate']:.1f}%)")
        print(f"⏱️  总耗时: {result['total_duration']:.1f} 秒")
        print(f"⚡ 平均速度: {result['avg_duration']:.1f} 秒/职位")
        
        if result.get("successful_jobs"):
            jobs = result["successful_jobs"]
            
            # 数据质量统计
            total_fields = 7
            field_counts = []
            for job in jobs:
                filled_fields = sum(1 for v in job.values() if v and v.strip())
                field_counts.append(filled_fields)
            
            avg_fields = sum(field_counts) / len(field_counts)
            print(f"📋 平均字段完整度: {avg_fields:.1f}/{total_fields} ({avg_fields/total_fields*100:.1f}%)")
            
            # 核心字段统计
            core_fields = ["岗位名称", "薪资情况", "工作地点"]
            core_complete = 0
            for job in jobs:
                if all(job.get(field, "").strip() for field in core_fields):
                    core_complete += 1
            
            print(f"🎯 核心字段完整: {core_complete}/{len(jobs)} ({core_complete/len(jobs)*100:.1f}%)")
        
        if result.get("json_file"):
            print(f"💾 数据文件: {result['json_file']}")
        if result.get("excel_file"):
            print(f"📊 Excel文件: {result['excel_file']}")
        if result.get("report_file"):
            print(f"📋 分析报告: {result['report_file']}")
    
    async def crawl_from_file(self, file_path: str, output_prefix: str = None) -> Dict[str, Any]:
        """从文件读取URL并爬取"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                urls = [line.strip() for line in f if line.strip() and line.strip().startswith("http")]
            
            if not urls:
                self.logger.error(f"文件中没有找到有效的URL: {file_path}")
                return {"error": "没有有效URL"}
            
            self.logger.info(f"从文件加载了 {len(urls)} 个URL: {file_path}")
            
            if output_prefix is None:
                output_prefix = f"boss_jobs_{int(time.time())}"
            
            return await self.crawl_batch_advanced(urls, output_prefix)
            
        except FileNotFoundError:
            self.logger.error(f"文件未找到: {file_path}")
            return {"error": "文件未找到"}
        except Exception as e:
            self.logger.error(f"读取文件失败: {str(e)}")
            return {"error": str(e)}

# 全局高级爬取器实例
advanced_crawler = AdvancedCrawler()
