"""
工作流管理器
完整的爬虫工作流程管理
"""

import asyncio
import time
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from smart_url_collector import smart_url_collector
from advanced_crawler import advanced_crawler
from performance_optimizer import performance_optimizer
from stats_analyzer import stats_analyzer
from config_manager import config_manager
from logger_manager import logger

class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self):
        self.workflow_id = None
        self.start_time = None
        self.workflow_stats = {}
    
    async def run_complete_workflow(self, workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行完整工作流"""
        self.workflow_id = f"workflow_{int(time.time())}"
        self.start_time = time.time()
        
        logger.info(f"开始完整工作流: {self.workflow_id}")
        
        try:
            # 第一阶段：URL收集
            urls = await self._stage_url_collection(workflow_config)
            
            if not urls:
                return {"error": "URL收集失败", "stage": "url_collection"}
            
            # 第二阶段：数据爬取
            crawl_result = await self._stage_data_crawling(urls, workflow_config)
            
            # 第三阶段：数据分析
            analysis_result = await self._stage_data_analysis(crawl_result)
            
            # 第四阶段：性能优化
            optimization_result = await self._stage_performance_optimization()
            
            # 生成最终报告
            final_report = self._generate_final_report(
                urls, crawl_result, analysis_result, optimization_result
            )
            
            logger.success(f"完整工作流完成: {self.workflow_id}")
            return final_report
            
        except Exception as e:
            logger.error(f"工作流异常: {str(e)}")
            return {"error": str(e), "workflow_id": self.workflow_id}
    
    async def _stage_url_collection(self, config: Dict[str, Any]) -> List[str]:
        """阶段1: URL收集"""
        logger.info("🔍 阶段1: URL收集")
        
        collection_method = config.get("collection_method", "search")
        
        if collection_method == "search":
            # 搜索收集
            search_terms = config.get("search_terms", [])
            max_pages = config.get("max_pages_per_term", 3)
            
            if search_terms:
                urls = await smart_url_collector.collect_by_search_terms(search_terms, max_pages)
            else:
                # 默认搜索
                urls = await smart_url_collector.collect_job_urls(
                    keyword=config.get("keyword", ""),
                    city=config.get("city", ""),
                    pages=config.get("pages", 5)
                )
        
        elif collection_method == "file":
            # 从文件读取
            file_path = config.get("url_file", "urls.txt")
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    urls = [line.strip() for line in f if line.strip() and line.strip().startswith("http")]
                logger.info(f"从文件加载了 {len(urls)} 个URL")
            except Exception as e:
                logger.error(f"读取URL文件失败: {str(e)}")
                urls = []
        
        elif collection_method == "manual":
            # 手动提供
            urls = config.get("urls", [])
        
        else:
            logger.error(f"未知的收集方法: {collection_method}")
            urls = []
        
        # 保存收集的URL
        if urls:
            url_file = smart_url_collector.save_urls_to_file(
                urls, f"output/{self.workflow_id}_collected_urls.txt"
            )
            logger.success(f"阶段1完成: 收集到 {len(urls)} 个URL")
        
        return urls
    
    async def _stage_data_crawling(self, urls: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
        """阶段2: 数据爬取"""
        logger.info("🚀 阶段2: 数据爬取")
        
        output_prefix = f"{self.workflow_id}_crawl_result"
        
        # 执行爬取
        crawl_result = await advanced_crawler.crawl_batch_advanced(urls, output_prefix)
        
        logger.success(f"阶段2完成: 成功爬取 {crawl_result['success_count']}/{crawl_result['total_count']} 个职位")
        
        return crawl_result
    
    async def _stage_data_analysis(self, crawl_result: Dict[str, Any]) -> Dict[str, Any]:
        """阶段3: 数据分析"""
        logger.info("📊 阶段3: 数据分析")
        
        if not crawl_result.get("successful_jobs"):
            logger.warning("没有成功的爬取数据，跳过分析阶段")
            return {"error": "没有数据可分析"}
        
        # 生成分析报告
        jobs_data = crawl_result["successful_jobs"]
        analysis_stats = stats_analyzer.analyze_jobs_data(jobs_data)
        
        # 保存分析报告
        report_file = f"output/{self.workflow_id}_analysis_report.txt"
        report_text = stats_analyzer.generate_report(jobs_data, report_file)
        
        logger.success(f"阶段3完成: 数据分析报告已生成")
        
        return {
            "stats": analysis_stats,
            "report_file": report_file,
            "report_text": report_text
        }
    
    async def _stage_performance_optimization(self) -> Dict[str, Any]:
        """阶段4: 性能优化"""
        logger.info("⚡ 阶段4: 性能优化")
        
        # 获取性能报告
        performance_report = performance_optimizer.get_performance_report()
        
        # 执行自动优化
        performance_optimizer.optimize_config()
        
        # 保存性能报告
        perf_file = f"output/{self.workflow_id}_performance_report.json"
        try:
            with open(perf_file, "w", encoding="utf-8") as f:
                json.dump(performance_report, f, indent=2, ensure_ascii=False)
            logger.success(f"性能报告已保存: {perf_file}")
        except Exception as e:
            logger.error(f"保存性能报告失败: {str(e)}")
        
        logger.success(f"阶段4完成: 性能优化建议已生成")
        
        return {
            "performance_report": performance_report,
            "report_file": perf_file
        }
    
    def _generate_final_report(self, urls: List[str], crawl_result: Dict[str, Any], 
                             analysis_result: Dict[str, Any], 
                             optimization_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终报告"""
        total_duration = time.time() - self.start_time
        
        final_report = {
            "workflow_id": self.workflow_id,
            "start_time": datetime.fromtimestamp(self.start_time).strftime("%Y-%m-%d %H:%M:%S"),
            "total_duration": total_duration,
            "stages": {
                "url_collection": {
                    "total_urls": len(urls),
                    "status": "success" if urls else "failed"
                },
                "data_crawling": {
                    "total_attempts": crawl_result.get("total_count", 0),
                    "successful_crawls": crawl_result.get("success_count", 0),
                    "success_rate": crawl_result.get("success_rate", 0),
                    "status": "success" if crawl_result.get("success_count", 0) > 0 else "failed"
                },
                "data_analysis": {
                    "status": "success" if not analysis_result.get("error") else "failed",
                    "report_file": analysis_result.get("report_file")
                },
                "performance_optimization": {
                    "status": "success",
                    "report_file": optimization_result.get("report_file")
                }
            },
            "summary": {
                "total_urls_collected": len(urls),
                "total_jobs_crawled": crawl_result.get("success_count", 0),
                "overall_success_rate": (crawl_result.get("success_count", 0) / len(urls)) * 100 if urls else 0,
                "average_crawl_time": crawl_result.get("avg_duration", 0),
                "data_quality_score": self._calculate_data_quality_score(crawl_result),
                "workflow_efficiency": self._calculate_workflow_efficiency(total_duration, crawl_result)
            },
            "files_generated": self._collect_generated_files(crawl_result, analysis_result, optimization_result),
            "recommendations": self._generate_recommendations(crawl_result, analysis_result, optimization_result)
        }
        
        # 保存最终报告
        final_report_file = f"output/{self.workflow_id}_final_report.json"
        try:
            with open(final_report_file, "w", encoding="utf-8") as f:
                json.dump(final_report, f, indent=2, ensure_ascii=False)
            logger.success(f"最终报告已保存: {final_report_file}")
            final_report["final_report_file"] = final_report_file
        except Exception as e:
            logger.error(f"保存最终报告失败: {str(e)}")
        
        return final_report
    
    def _calculate_data_quality_score(self, crawl_result: Dict[str, Any]) -> float:
        """计算数据质量分数"""
        if not crawl_result.get("successful_jobs"):
            return 0.0
        
        jobs = crawl_result["successful_jobs"]
        total_fields = 7
        total_score = 0
        
        for job in jobs:
            filled_fields = sum(1 for v in job.values() if v and v.strip())
            score = (filled_fields / total_fields) * 100
            total_score += score
        
        return total_score / len(jobs)
    
    def _calculate_workflow_efficiency(self, total_duration: float, crawl_result: Dict[str, Any]) -> float:
        """计算工作流效率"""
        total_jobs = crawl_result.get("total_count", 1)
        efficiency = (crawl_result.get("success_count", 0) / total_jobs) * (3600 / total_duration)  # 每小时成功数
        return min(100, efficiency * 10)  # 标准化到100分制
    
    def _collect_generated_files(self, crawl_result: Dict[str, Any], 
                               analysis_result: Dict[str, Any], 
                               optimization_result: Dict[str, Any]) -> List[str]:
        """收集生成的文件"""
        files = []
        
        if crawl_result.get("json_file"):
            files.append(crawl_result["json_file"])
        if crawl_result.get("excel_file"):
            files.append(crawl_result["excel_file"])
        if analysis_result.get("report_file"):
            files.append(analysis_result["report_file"])
        if optimization_result.get("report_file"):
            files.append(optimization_result["report_file"])
        
        return files
    
    def _generate_recommendations(self, crawl_result: Dict[str, Any], 
                                analysis_result: Dict[str, Any], 
                                optimization_result: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        success_rate = crawl_result.get("success_rate", 0)
        
        if success_rate >= 90:
            recommendations.append("爬取成功率优秀，当前配置效果很好")
        elif success_rate >= 70:
            recommendations.append("爬取成功率良好，可以考虑优化网络配置")
        else:
            recommendations.append("爬取成功率偏低，建议检查反反爬策略和网络环境")
        
        # 添加性能优化建议
        if optimization_result.get("performance_report", {}).get("recommendations"):
            recommendations.extend(optimization_result["performance_report"]["recommendations"])
        
        return recommendations

# 全局工作流管理器实例
workflow_manager = WorkflowManager()
