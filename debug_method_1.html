<!DOCTYPE html><html><head>
    <meta charset="utf-8">
    <title>您访问的页面不存在-BOSS直聘</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta name="keywords" content="页面不存在,404">
    <meta name="description" content="您访问的页面不存在，请访问BOSS直聘首页或访问BOSS直聘APP查看相关内容。">
    <link rel="shortcut icon" href="https://static.zhipin.com/favicon.ico">
    <style>
        * { margin:0; padding:0; }
        html,body { height:100%; }
        body { background-color:#EEF0F5; }
        body,button { font-family: arial, verdana, helvetica,'PingFang SC','HanHei SC','STHeitiSC-Light', Microsoft Yahei,sans-serif; font-size: 14px; line-height: 24px; color: #414a60; -webkit-font-smoothing: antialiased;}
        a { text-decoration:none; }
        #wrap { position:relative; min-height:100%; }
        .btn { display:inline-block; width:98px; height:32px; line-height:32px; border:1px #62D5C8 solid; color:#5dd5c8; text-align:center; margin-right:20px; }
        .btn:hover { background-color:#fff; }
        .error-content { position:absolute; width:644px; min-height:260px; top:50%; left:50%; margin-left:-322px; margin-top:-130px; background:url(https://static.zhipin.com/v2/web/boss/images/icon-page-error.png) right center no-repeat; }
        .error-content h1 { font-size:30px; line-height:36px; padding-top:30px; }
        .error-content h3 { padding:20px 0 18px; font-size:18px; font-weight: normal; }
        .error-content h3.gray { color:#9fa3b0;}
        .error-content p { color:#9fa3b0; max-width:300px; }
        .error-content .btns { padding-top:30px; }

        #footer { position:absolute; width:100%; bottom:10px; text-align:center; color:#bec3d1; }
        @media (max-width: 760px) {
            .error-content { top: 50%; left: 50%; transform: translate(-50%, -50%); margin: 0 auto; width: auto; background-position: center 30px; background-size: 200px auto; text-align: center; padding-top: 150px; }
            .error-content h1 { font-size: 24px; }
            .error-content h3 { font-size: 15px; padding: 10px 0 10px; }
            .btns { white-space: nowrap; }
            #footer { font-size: 12px; }
        }
        .gray-text {
            color: #9fa3b0;
            margin-top: -16px;
            font-weight: 400;
        }
        #second {
            color: #5dd5c8;
            padding: 0 2px;
        }
    </style>
</head>
<body>
<div id="wrap">
    <div id="main">
        <div class="error-content">
            <div class="text">
                <h1>Oops!</h1>
                <h3 class="gray">您访问的页面不存在～</h3>
                <h4 class="gray-text">将于<span id="second">1</span>秒后自动跳转首页</h4>
                <div class="btns"><a href="/" class="btn" ka="click-index">首页</a></div>
            </div>
        </div>
    </div>
    <div id="footer">
        <p>© copyright BOSS直聘 2025 <a href="https://beian.miit.gov.cn/" rel="nofollow" ka="link-icp" target="_blank">京ICP备14013441号-5</a> 京ICP证150923号</p>
    </div>
</div>
<input type="hidden" id="page_key_name" value="404">
<script src="//hm.baidu.com/hm.js?194df3105ad7148dcf2b98a91b5e727a"></script><script src="https://static.zhipin.com/library/js/analytics/ka.zhipin.min.js?v=5304" crossorigin="anonymous"></script><script type="text/javascript">
    (function(){
        var El = document.getElementById('second');
        var num = Number(El.innerHTML);
        var timer = setInterval(function() {

            num --;
            if(num == 0) {
                clearInterval(timer);
                try{
                    _T.sendEvent('auto_redirect');
                }catch(e){}
                window.location.href = '/';
            }else{
                El.innerHTML = num;
            }

        }, 1000);
    })();
</script>
<script>
    function get_share_datas_from_html_inapp() {
        var shid = "shdefault",
                urlShid,
                urlSid,
                pk = "pkdefault",
                pp = "ppdefault",
                pkn = (typeof _PK != 'undefined' ? _PK : document.getElementById("page_key_name")),
                getQueryString = function(name) {
                    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"),
                            r = window.location.search.substr(1).match(reg);
                    if (r != null) {
                        return unescape(r[2])
                    }
                    return null;
                };
        urlShid = getQueryString("shid");
        urlSid = getQueryString("sid");
        if (urlShid) {
            shid = urlShid;
        } else if (urlSid) {
            shid = urlSid;
        }
        if (pkn) {
            var pknVal = pkn.value;
            if (pknVal) {
                var pkArray = pknVal.split("|");
                if (pkArray.length == 1) {
                    pk = pkArray[0];
                } else if (pkArray.length >= 2) {
                    pk = pkArray[0];
                    pp = pkArray[1];
                }
            }
        }
        var ret = [];
        ret["shid"] = shid;
        ret["pk"] = pk;
        ret["pp"] = pp;
        return ret;
    }
    function getQueryString(name)
    {
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if(r!=null)return  unescape(r[2]); return null;
    }
</script>
<script>
    var _T = _T || [];
    (function() {
        var script = document.createElement("script");
        script.src = "https://static.zhipin.com/library/js/analytics/ka.zhipin.min.js?v=5304";
        script.setAttribute('crossorigin', 'anonymous');
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(script, s);
    })();

    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "//hm.baidu.com/hm.js?194df3105ad7148dcf2b98a91b5e727a";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script><script type="text/javascript">
    (function() {
        var image = new Image();
        image.src = 'https://t.zhipin.com/f.gif?pk=web_404&r=' + encodeURIComponent(document.referrer);
    })();
</script>

</body></html>